2025-06-20 19:57:19,020 INFO: 应用启动 - PID: 5140 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-22 17:39:50,873 INFO: 应用启动 - PID: 5140 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 17:42:37,890 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('food_safety', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 17:43:47,243 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
