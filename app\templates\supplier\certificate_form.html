{% extends 'base.html' %}

{% block title %}{{ title }} - {{ super() }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">{{ title }}</h3>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('supplier_certificate.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data">
                        {{ form.hidden_tag() }}
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.supplier_id.label }}
                                    {{ form.supplier_id(class="form-control") }}
                                    {% for error in form.supplier_id.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.certificate_type.label }}
                                    {{ form.certificate_type(class="form-control") }}
                                    {% for error in form.certificate_type.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.certificate_number.label }}
                                    {{ form.certificate_number(class="form-control") }}
                                    {% for error in form.certificate_number.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.issuing_authority.label }}
                                    {{ form.issuing_authority(class="form-control") }}
                                    {% for error in form.issuing_authority.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    {{ form.issue_date.label }}
                                    {{ form.issue_date(class="form-control datepicker") }}
                                    {% for error in form.issue_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.expiry_date.label }}
                                    {{ form.expiry_date(class="form-control datepicker") }}
                                    {% for error in form.expiry_date.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                </div>
                                <div class="form-group">
                                    {{ form.certificate_image.label }}
                                    <div class="custom-file">
                                        {{ form.certificate_image(class="custom-file-input") }}
                                        <label class="custom-file-label" for="certificate_image">选择文件</label>
                                    </div>
                                    {% for error in form.certificate_image.errors %}
                                    <small class="text-danger">{{ error }}</small>
                                    {% endfor %}
                                    {% if certificate and certificate.certificate_image %}
                                    <div class="mt-2">
                                        <img src="{{ url_for('static', filename=certificate.certificate_image) }}" alt="证书图片" class="img-thumbnail" style="max-height: 100px;">
                                    </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            {{ form.submit(class="btn btn-primary") }}
                            <a href="{{ url_for('supplier_certificate.index') }}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 日期选择器
        $('.datepicker').datepicker({
            format: 'yyyy-mm-dd',
            autoclose: true,
            todayHighlight: true,
            language: 'zh-CN'
        });

        // 文件上传显示文件名
        $('.custom-file-input').on('change', function() {
            var fileName = $(this).val().split('\\').pop();
            $(this).next('.custom-file-label').html(fileName);
        });

        // 如果URL中有supplier_id参数，自动选择对应的供应商
        var urlParams = new URLSearchParams(window.location.search);
        var supplierIdParam = urlParams.get('supplier_id');
        if (supplierIdParam) {
            $('#supplier_id').val(supplierIdParam);
        }
    });
</script>
{% endblock %}
