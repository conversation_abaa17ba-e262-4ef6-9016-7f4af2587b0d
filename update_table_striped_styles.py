#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表格条纹样式更新工具
将所有 table-striped 背景色替换为系统主题变量
"""

import os
import re
from pathlib import Path
from datetime import datetime

def find_table_striped_styles():
    """查找所有 table-striped 相关的样式定义"""
    
    results = {
        "css_files": [],
        "html_files": [],
        "summary": {
            "total_css_files": 0,
            "total_html_files": 0,
            "css_matches": 0,
            "html_matches": 0
        }
    }
    
    # CSS文件中的 table-striped 样式模式
    css_patterns = {
        "table_striped": re.compile(r'(\.table-striped[^{]*\{[^}]*background[^}]*\})', re.IGNORECASE | re.DOTALL),
        "striped_tbody": re.compile(r'(\.table-striped\s+tbody[^{]*\{[^}]*background[^}]*\})', re.IGNORECASE | re.DOTALL),
        "striped_tr": re.compile(r'(\.table-striped[^{]*tr[^{]*\{[^}]*background[^}]*\})', re.IGNORECASE | re.DOTALL),
        "nth_child": re.compile(r'(\.table-striped[^{]*:nth-[^{]*\{[^}]*background[^}]*\})', re.IGNORECASE | re.DOTALL),
        "background_prop": re.compile(r'background[^:]*:\s*([^;!]+)(!important)?', re.IGNORECASE)
    }
    
    # HTML文件中的内联样式模式
    html_patterns = {
        "style_blocks": re.compile(r'<style[^>]*>(.*?)</style>', re.DOTALL | re.IGNORECASE),
        "table_striped_styles": re.compile(r'(\.table-striped[^{]*\{[^}]*background[^}]*\})', re.IGNORECASE | re.DOTALL)
    }
    
    def process_css_file(file_path):
        """处理CSS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取CSS文件失败 {file_path}: {e}")
            return
        
        results["summary"]["total_css_files"] += 1
        file_matches = []
        
        # 查找所有 table-striped 相关样式
        for pattern_name, pattern in css_patterns.items():
            if pattern_name == "background_prop":
                continue
                
            matches = pattern.findall(content)
            for match in matches:
                # 提取背景色属性
                bg_matches = css_patterns["background_prop"].findall(match)
                if bg_matches:
                    line_num = content[:content.find(match)].count('\n') + 1
                    file_matches.append({
                        "pattern": pattern_name,
                        "rule": match,
                        "background_values": bg_matches,
                        "line_number": line_num
                    })
                    results["summary"]["css_matches"] += 1
        
        if file_matches:
            results["css_files"].append({
                "file": str(file_path),
                "matches": file_matches
            })
    
    def process_html_file(file_path):
        """处理HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取HTML文件失败 {file_path}: {e}")
            return
        
        results["summary"]["total_html_files"] += 1
        file_matches = []
        
        # 查找样式块中的 table-striped 样式
        style_blocks = html_patterns["style_blocks"].findall(content)
        for style_content in style_blocks:
            striped_matches = html_patterns["table_striped_styles"].findall(style_content)
            for match in striped_matches:
                bg_matches = css_patterns["background_prop"].findall(match)
                if bg_matches:
                    line_num = content[:content.find(style_content)].count('\n') + 1
                    file_matches.append({
                        "rule": match,
                        "background_values": bg_matches,
                        "line_number": line_num,
                        "style_block": style_content
                    })
                    results["summary"]["html_matches"] += 1
        
        if file_matches:
            results["html_files"].append({
                "file": str(file_path),
                "matches": file_matches
            })
    
    # 扫描CSS文件
    css_dirs = [
        Path("app/static/css"),
        Path("app/static/financial/css"),
        Path("app/static/vendor")
    ]
    
    for css_dir in css_dirs:
        if css_dir.exists():
            for css_file in css_dir.rglob("*.css"):
                process_css_file(css_file)
    
    # 扫描HTML文件
    templates_dir = Path("app/templates")
    if templates_dir.exists():
        for html_file in templates_dir.rglob("*.html"):
            process_html_file(html_file)
    
    return results

def update_table_striped_styles(results):
    """更新 table-striped 样式为系统主题变量"""
    
    updated_files = []
    
    # 定义替换规则
    replacement_rules = {
        # 常见的背景色值替换为主题变量
        r'#f8f9fa': 'var(--theme-gray-100, #f8f9fa)',
        r'#f2f2f2': 'var(--theme-gray-100, #f8f9fa)',
        r'rgba\(0,0,0,0\.05\)': 'var(--theme-striped-bg, rgba(0,0,0,0.05))',
        r'rgba\(0,0,0,\.05\)': 'var(--theme-striped-bg, rgba(0,0,0,0.05))',
        r'var\(--enterprise-gray-50\)': 'var(--theme-gray-100, #f8f9fa)',
        r'#e9ecef': 'var(--theme-gray-200, #e9ecef)',
        r'#dee2e6': 'var(--theme-gray-300, #dee2e6)'
    }
    
    def update_css_file(file_info):
        """更新CSS文件"""
        file_path = Path(file_info["file"])
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        original_content = content
        changes_made = []
        
        # 应用替换规则
        for old_value, new_value in replacement_rules.items():
            if re.search(old_value, content, re.IGNORECASE):
                content = re.sub(old_value, new_value, content, flags=re.IGNORECASE)
                changes_made.append(f"{old_value} → {new_value}")
        
        # 如果有更改，保存文件
        if content != original_content:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                updated_files.append({
                    "file": str(file_path),
                    "changes": changes_made
                })
                return True
            except Exception as e:
                print(f"保存文件失败 {file_path}: {e}")
                return False
        
        return False
    
    def update_html_file(file_info):
        """更新HTML文件"""
        file_path = Path(file_info["file"])
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        original_content = content
        changes_made = []
        
        # 应用替换规则
        for old_value, new_value in replacement_rules.items():
            if re.search(old_value, content, re.IGNORECASE):
                content = re.sub(old_value, new_value, content, flags=re.IGNORECASE)
                changes_made.append(f"{old_value} → {new_value}")
        
        # 如果有更改，保存文件
        if content != original_content:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                updated_files.append({
                    "file": str(file_path),
                    "changes": changes_made
                })
                return True
            except Exception as e:
                print(f"保存文件失败 {file_path}: {e}")
                return False
        
        return False
    
    # 更新CSS文件
    for file_info in results["css_files"]:
        update_css_file(file_info)
    
    # 更新HTML文件
    for file_info in results["html_files"]:
        update_html_file(file_info)
    
    return updated_files

def add_theme_variables():
    """在主题文件中添加条纹表格相关的CSS变量"""
    
    theme_file = Path("app/static/css/theme-colors.css")
    
    if not theme_file.exists():
        print(f"主题文件不存在: {theme_file}")
        return False
    
    try:
        with open(theme_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取主题文件失败: {e}")
        return False
    
    # 检查是否已经有条纹表格变量
    if '--theme-striped-bg' in content:
        print("主题变量已存在，跳过添加")
        return True
    
    # 在 :root 块中添加条纹表格变量
    root_pattern = r'(:root\s*\{[^}]*)(})'
    
    new_variables = '''
  /* 表格条纹背景色 */
  --theme-striped-bg: rgba(0, 0, 0, 0.05);
  --theme-striped-bg-hover: rgba(var(--theme-primary-rgb), 0.1);
'''
    
    def replace_root(match):
        return match.group(1) + new_variables + '\n' + match.group(2)
    
    updated_content = re.sub(root_pattern, replace_root, content, flags=re.DOTALL)
    
    if updated_content != content:
        try:
            with open(theme_file, 'w', encoding='utf-8') as f:
                f.write(updated_content)
            print(f"✅ 已在 {theme_file} 中添加条纹表格主题变量")
            return True
        except Exception as e:
            print(f"保存主题文件失败: {e}")
            return False
    
    return False

def generate_report(results, updated_files):
    """生成更新报告"""
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# Table-Striped 样式更新报告",
        "",
        f"**更新时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 扫描结果统计",
        "",
        f"- **扫描CSS文件数**: {results['summary']['total_css_files']}",
        f"- **扫描HTML文件数**: {results['summary']['total_html_files']}",
        f"- **CSS中发现的匹配项**: {results['summary']['css_matches']}",
        f"- **HTML中发现的匹配项**: {results['summary']['html_matches']}",
        f"- **实际更新的文件数**: {len(updated_files)}",
        "",
    ])
    
    # 更新的文件详情
    if updated_files:
        report_lines.extend([
            "## 🔄 已更新的文件",
            "",
        ])
        
        for file_info in updated_files:
            report_lines.append(f"### `{file_info['file']}`")
            report_lines.append("")
            report_lines.append("**更改内容**:")
            for change in file_info['changes']:
                report_lines.append(f"- {change}")
            report_lines.append("")
            report_lines.append("---")
            report_lines.append("")
    
    # 发现但未更新的文件
    css_files_with_matches = [f for f in results["css_files"] if f["file"] not in [u["file"] for u in updated_files]]
    html_files_with_matches = [f for f in results["html_files"] if f["file"] not in [u["file"] for u in updated_files]]
    
    if css_files_with_matches or html_files_with_matches:
        report_lines.extend([
            "## ⚠️ 发现但未自动更新的文件",
            "",
            "这些文件包含 table-striped 样式，但可能需要手动检查：",
            "",
        ])
        
        for file_info in css_files_with_matches + html_files_with_matches:
            report_lines.append(f"- `{file_info['file']}`")
        
        report_lines.append("")
    
    # 优化建议
    report_lines.extend([
        "## 💡 优化建议",
        "",
        "### 🎯 主题变量使用",
        "",
        "1. **统一条纹背景**: 使用 `var(--theme-gray-100, #f8f9fa)` 作为条纹背景",
        "2. **悬停效果**: 使用 `var(--theme-striped-bg-hover)` 作为悬停背景",
        "3. **主题一致性**: 确保所有条纹表格使用相同的主题变量",
        "",
        "### 🔧 CSS变量定义",
        "",
        "已在 `theme-colors.css` 中添加以下变量：",
        "```css",
        "--theme-striped-bg: rgba(0, 0, 0, 0.05);",
        "--theme-striped-bg-hover: rgba(var(--theme-primary-rgb), 0.1);",
        "```",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始扫描 table-striped 样式...")
    
    # 1. 查找所有 table-striped 样式
    results = find_table_striped_styles()
    
    print(f"📊 扫描完成:")
    print(f"   - CSS文件: {results['summary']['total_css_files']} 个")
    print(f"   - HTML文件: {results['summary']['total_html_files']} 个")
    print(f"   - CSS匹配项: {results['summary']['css_matches']} 个")
    print(f"   - HTML匹配项: {results['summary']['html_matches']} 个")
    
    # 2. 添加主题变量
    print("\n🎨 添加主题变量...")
    add_theme_variables()
    
    # 3. 更新样式
    print("\n🔄 更新样式...")
    updated_files = update_table_striped_styles(results)
    
    print(f"✅ 更新完成，共更新 {len(updated_files)} 个文件")
    
    # 4. 生成报告
    print("\n📝 生成更新报告...")
    report_content = generate_report(results, updated_files)
    
    report_file = "table_striped_update_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 报告已生成: {report_file}")
    
    # 显示更新的文件
    if updated_files:
        print("\n🎯 已更新的文件:")
        for file_info in updated_files:
            print(f"   - {file_info['file']}")

if __name__ == "__main__":
    main()
