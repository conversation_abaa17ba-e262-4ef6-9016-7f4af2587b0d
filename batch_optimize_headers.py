#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量优化页面头部布局
将所有需要优化的页面头部改为统一的flex布局
"""

import os
import re
from pathlib import Path

def optimize_page_headers():
    """批量优化页面头部"""
    
    # 需要优化的页面列表（从分析报告中提取）
    pages_to_optimize = [
        "admin/carousel_batch_upload.html",
        "admin/carousel_form.html", 
        "admin/super_delete/index.html",
        "admin/system/monitor.html",
        "daily_management/inspection_form.html",
        "daily_management/photo_upload_qrcode.html",
        "employee/daily_health_check.html",
        "financial/assistant/quick_create.html",
        "financial/vouchers/create.html",
        "financial/vouchers/index.html",
        "food_trace/qr_scan.html",
        "ingredient/form.html",
        "main/index.html",
        "stock_in/wizard_simple.html",
        "traceability/trace_interface_new.html",
        "traceability/trace_interface_simple.html"
    ]
    
    templates_dir = Path("app/templates")
    
    # 标准的优化模式
    def get_optimized_header(title_content, return_url, return_text="返回列表"):
        return f'''                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">{title_content}</h3>
                        </div>
                        <div class="card-tools">
                            <a href="{return_url}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> {return_text}
                            </a>
                        </div>
                    </div>
                </div>'''
    
    # 处理每个页面
    for page_path in pages_to_optimize:
        file_path = templates_dir / page_path
        
        if not file_path.exists():
            print(f"文件不存在: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找卡片头部模式
            header_patterns = [
                # 模式1: 简单的h3标题
                (r'<div class="card-header">\s*<h3 class="card-title">([^<]+)</h3>\s*</div>', 
                 lambda m: get_optimized_header(m.group(1), "{{ url_for('main.index') }}", "返回首页")),
                
                # 模式2: 带图标的h3标题
                (r'<div class="card-header">\s*<h3 class="card-title">(<i[^>]*></i>[^<]+)</h3>\s*</div>',
                 lambda m: get_optimized_header(m.group(1), "{{ url_for('main.index') }}", "返回首页")),
                
                # 模式3: 带变量的h3标题
                (r'<div class="card-header">\s*<h3 class="card-title">(\{\{[^}]+\}\})</h3>\s*</div>',
                 lambda m: get_optimized_header(m.group(1), "{{ url_for('main.index') }}", "返回首页")),
            ]
            
            # 尝试匹配和替换
            modified = False
            for pattern, replacement_func in header_patterns:
                if re.search(pattern, content, re.IGNORECASE | re.DOTALL):
                    content = re.sub(pattern, replacement_func, content, flags=re.IGNORECASE | re.DOTALL)
                    modified = True
                    break
            
            if modified:
                # 写回文件
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 已优化: {page_path}")
            else:
                print(f"⚠️  未找到匹配模式: {page_path}")
                
        except Exception as e:
            print(f"❌ 处理失败 {page_path}: {e}")

def optimize_specific_pages():
    """优化特定页面的特殊情况"""
    
    templates_dir = Path("app/templates")
    
    # 特殊页面的优化规则
    special_optimizations = {
        "financial/vouchers/index.html": {
            "pattern": r'<div class="card-header">\s*<h3 class="card-title">\$\{title\}</h3>\s*</div>',
            "replacement": '''                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h3 class="card-title mb-0">${title}</h3>
                        </div>
                        <div class="card-tools">
                            <a href="{{ url_for('financial.index') }}" class="btn btn-secondary btn-sm">
                                <i class="fas fa-arrow-left"></i> 返回财务管理
                            </a>
                        </div>
                    </div>
                </div>'''
        },
        
        "main/index.html": {
            "pattern": r'<h3[^>]*>([^<]+)</h3>',
            "replacement": '''<div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="mb-0">\\1</h3>
                    </div>'''
        }
    }
    
    for page_path, optimization in special_optimizations.items():
        file_path = templates_dir / page_path
        
        if not file_path.exists():
            print(f"特殊文件不存在: {file_path}")
            continue
            
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if re.search(optimization["pattern"], content, re.IGNORECASE | re.DOTALL):
                content = re.sub(optimization["pattern"], optimization["replacement"], content, flags=re.IGNORECASE | re.DOTALL)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 已优化特殊页面: {page_path}")
            else:
                print(f"⚠️  特殊页面未找到匹配模式: {page_path}")
                
        except Exception as e:
            print(f"❌ 特殊页面处理失败 {page_path}: {e}")

if __name__ == "__main__":
    print("🚀 开始批量优化页面头部布局...")
    print("=" * 50)
    
    # 批量优化
    optimize_page_headers()
    
    print("\n" + "=" * 50)
    print("🎯 优化特殊页面...")
    
    # 优化特殊页面
    optimize_specific_pages()
    
    print("\n" + "=" * 50)
    print("✨ 批量优化完成！")
    print("\n建议:")
    print("1. 检查修改后的页面是否正常显示")
    print("2. 测试返回按钮的链接是否正确")
    print("3. 根据需要调整返回链接的目标页面")
