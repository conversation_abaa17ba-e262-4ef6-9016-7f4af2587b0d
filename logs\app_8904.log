2025-06-21 07:23:45,365 INFO: 应用启动 - PID: 8904 [in C:\StudentsCMSSP\app\__init__.py:831]
2025-06-22 18:12:36,640 INFO: 应用启动 - PID: 8904 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 18:13:35,617 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('payment_records', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:14:02,159 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('special_events', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:14:02,235 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('weekly_menu_plan', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:14:07,369 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('balance_sheet', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:15:34,311 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-22 18:16:38,302 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 18:16:38,309 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 18:16:38,315 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 18:16:38,355 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 18:16:38,359 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 18:16:38,369 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 18:16:38,372 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 18:16:38,375 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 18:16:46,020 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('daily_overview', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:17:49,493 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('training_records', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
