import { type PipelinePolicy } from "../pipeline.js";
export declare const wrapAbortSignalLikePolicyName = "wrapAbortSignalLikePolicy";
/**
 * Policy that ensure that any AbortSignalLike is wrapped in a native AbortSignal for processing by the pipeline.
 * Since the ts-http-runtime expects a native AbortSignal, this policy is used to ensure that any AbortSignalLike is wrapped in a native AbortSignal.
 *
 * @returns - created policy
 */
export declare function wrapAbortSignalLikePolicy(): PipelinePolicy;
//# sourceMappingURL=wrapAbortSignalLikePolicy.d.ts.map