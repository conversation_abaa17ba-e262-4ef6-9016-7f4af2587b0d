# 表头样式类扫描报告

**生成时间**: 2025-06-22 20:00:13

## 📊 统计概览

- **扫描文件总数**: 384
- **包含表格的文件数**: 200
- **table标签总数**: 396
- **thead标签总数**: 303
- **th标签总数**: 2625
- **唯一table类数**: 51
- **唯一thead类数**: 3
- **唯一th类数**: 81

## 📁 按文件分组的表头样式

### `admin\carousel_list.html`

**Table标签样式类**:
- `table table-striped table-hover`

**Thead标签样式类**:
- `table-dark`

**Th标签样式类**:
- `table-dark`

---

### `admin\guide_management\demo_data.html`

**Table标签样式类**:
- `table table-striped`

---

### `admin\guide_management\scenarios.html`

**Table标签样式类**:
- `table table-sm`

---

### `admin\guide_management\users.html`

**Table标签样式类**:
- `table table-striped`

---

### `admin\roles.html`

**Table标签样式类**:
- `table table-hover`
- `table table-hover`
- `table table-hover`
- `table table-hover`
- `table table-hover`

---

### `admin\super_delete\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `admin\system\backups.html`

**Table标签样式类**:
- `table table-hover backup-table`

---

### `admin\system\module_visibility.html`

**Table标签样式类**:
- `table table-bordered table-hover`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `w-30`
- `w-60`

---

### `admin\system\monitor.html`

**Table标签样式类**:
- `table table-sm system-info`
- `table table-sm table-hover`
- `table table-sm table-hover`

---

### `admin\users.html`

**Table标签样式类**:
- `table table-hover`

---

### `admin\view_role.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-sm`
- `table table-hover`

**Th标签样式类**:
- `w-30`

---

### `admin\view_user.html`

**Table标签样式类**:
- `table table-sm`
- `table table-bordered table-hover mb-0`
- `table table-sm`
- `table table-hover`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`

---

### `area\index.html`

**Table标签样式类**:
- `table table-sm`
- `table table-hover`

---

### `area\view_area.html`

**Table标签样式类**:
- `table table-hover table-striped`
- `table table-hover table-striped`
- `table table-hover table-striped`

**Thead标签样式类**:
- `thead-light`
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `thead-light`
- `thead-light`

---

### `batch_flow\form.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-30`

---

### `components\page_layout.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `consultation\detail.html`

**Table标签样式类**:
- `table table-bordered`

---

### `consultation\list.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `consumption_plan\create.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `consumption_plan\create_from_weekly.html`

**Table标签样式类**:
- `table table-bordered`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`

---

### `consumption_plan\edit.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `consumption_plan\index.html`

**Table标签样式类**:
- `table table-compact table-hover table-bordered`

---

### `consumption_plan\new.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `consumption_plan\super_editor.html`

**Table标签样式类**:
- `table table-bordered table-hover`

**Th标签样式类**:
- `w-15`
- `w-15`
- `w-15`

---

### `consumption_plan\view.html`

**Table标签样式类**:
- `info-table`
- `items-table`
- `info-table`

---

### `daily_management\companions.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\events.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\index.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\inspection_table_html.html`

**Table标签样式类**:
- `inspection-table`

---

### `daily_management\inspections.html`

**Table标签样式类**:
- `table table-bordered inspection-table`
- `table table-bordered inspection-table`
- `table table-bordered inspection-table`

---

### `daily_management\inspections_card_layout.html`

**Table标签样式类**:
- `inspection-table`
- `inspection-table`
- `inspection-table`

---

### `daily_management\inspections_new.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `daily_management\inspections_simple_table.html`

**Table标签样式类**:
- `inspection-table`

**Th标签样式类**:
- `time-header`
- `item-header`
- `item-header`
- `item-header`
- `item-header`
- `item-header`
- `item-header`

---

### `daily_management\inspections_table.html`

**Table标签样式类**:
- `inspection-table`

---

### `daily_management\issues.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\logs.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\optimized_dashboard.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\print\companion_report.html`

---

### `daily_management\print\daily_summary.html`

---

### `daily_management\print\event_report.html`

---

### `daily_management\print\inspection_report.html`

---

### `daily_management\print\issue_report.html`

---

### `daily_management\print\print_companions.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\print\print_events.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\print\print_inspections.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`

---

### `daily_management\print\print_issues.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\print\print_log.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`

---

### `daily_management\print\print_trainings.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\print\training_report.html`

---

### `daily_management\print_inspection_photo_detail.html`

**Table标签样式类**:
- `info-table`

---

### `daily_management\print_log.html`

**Table标签样式类**:
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`
- `table`

---

### `daily_management\print_training.html`

**Table标签样式类**:
- `table`

---

### `daily_management\simplified_inspection.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`

---

### `daily_management\trainings.html`

**Table标签样式类**:
- `table table-bordered`

---

### `daily_management\view_issue.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `daily_management\view_training.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `data_repair\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `employee\daily_health_check.html`

**Table标签样式类**:
- `table table-hover`
- `table table-hover`

---

### `employee\health_certificates.html`

**Table标签样式类**:
- `table table-hover`

---

### `employee\index.html`

**Table标签样式类**:
- `table table-hover`

---

### `employee\view_employee.html`

**Table标签样式类**:
- `table table-sm`
- `table table-sm`
- `table table-sm`
- `table table-hover`
- `table table-hover`
- `table table-hover`
- `table table-hover`

---

### `financial\accounting_subjects\index.html`

**Table标签样式类**:
- `uf-table`

---

### `financial\assistant\index.html`

**Table标签样式类**:
- `table table-striped`

---

### `financial\assistant\quick_create.html`

**Table标签样式类**:
- `preview-table`
- `table table-sm`

---

### `financial\ledgers\balance.html`

**Table标签样式类**:
- `uf-table uf-balance-sheet-table`

**Th标签样式类**:
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`

---

### `financial\ledgers\detail.html`

**Table标签样式类**:
- `uf-table uf-generated-ledgers-table`
- `uf-table uf-ledger-table`

**Th标签样式类**:
- `uf-col-period`
- `uf-col-subject`
- `uf-col-type`
- `uf-col-opening`
- `uf-col-debit`
- `uf-col-credit`
- `uf-col-ending`
- `uf-col-records`
- `uf-col-generated`
- `uf-col-operations`
- `uf-col-line-no`
- `uf-col-date sortable`
- `uf-col-voucher sortable`
- `uf-col-summary`
- `uf-col-debit sortable`
- `uf-col-credit sortable`
- `uf-col-balance sortable`
- `uf-col-operations`

---

### `financial\ledgers\general.html`

**Table标签样式类**:
- `uf-table uf-general-ledger-table`
- `uf-table uf-summary-table`

**Th标签样式类**:
- `uf-col-checkbox`
- `uf-col-code sortable`
- `uf-col-name sortable`
- `uf-col-type sortable`
- `uf-col-direction`
- `uf-col-opening sortable`
- `uf-col-debit sortable`
- `uf-col-credit sortable`
- `uf-col-ending sortable`
- `uf-col-operations`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `text-center`

---

### `financial\payables\index.html`

**Table标签样式类**:
- `uf-table`

---

### `financial\payables\pending_stock_ins.html`

**Table标签样式类**:
- `uf-financial-table`

**Th标签样式类**:
- `col-voucher`
- `col-subject-name`
- `col-date`
- `col-amount`
- `col-voucher`
- `col-date`
- `col-actions`

---

### `financial\payments\index.html`

**Table标签样式类**:
- `uf-table`

---

### `financial\reports\balance_sheet.html`

**Table标签样式类**:
- `uf-table`
- `uf-table`

---

### `financial\reports\cost_analysis.html`

**Table标签样式类**:
- `uf-table`
- `uf-table`

---

### `financial\reports\income_statement.html`

**Table标签样式类**:
- `uf-table uf-cost-analysis-table`

**Th标签样式类**:
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`
- `uf-amount-col`

---

### `financial\reports\payables_aging.html`

**Table标签样式类**:
- `uf-table`
- `uf-table`

---

### `financial\reports\trial_balance.html`

**Table标签样式类**:
- `table financial-table`

**Th标签样式类**:
- `text-right`
- `text-right`
- `text-right`
- `text-right`
- `text-right`
- `text-right`
- `text-right`
- `text-right`

---

### `financial\reports\voucher_summary.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`

**Thead标签样式类**:
- `thead-light`
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `text-right`
- `text-right`
- `text-right`
- `thead-light`
- `text-right`
- `text-right`
- `text-right`
- `thead-light`
- `text-right`

---

### `financial\vouchers\create.html`

**Table标签样式类**:
- `uf-table uf-voucher-table`
- `uf-table uf-subject-table`

**Th标签样式类**:
- `uf-col-sequence`
- `uf-col-summary`
- `uf-col-subject`
- `uf-col-debit`
- `uf-col-credit`
- `uf-col-operations`
- `uf-col-code`
- `uf-col-name`
- `uf-col-type`
- `uf-col-level`
- `uf-col-direction`

---

### `financial\vouchers\edit.html`

**Table标签样式类**:
- `voucher-table`

---

### `financial\vouchers\edit_professional.html`

**Table标签样式类**:
- `table table-bordered voucher-editor-table`

**Thead标签样式类**:
- `thead-dark`

**Th标签样式类**:
- `thead-dark`

---

### `financial\vouchers\index.html`

**Table标签样式类**:
- `table table-sm mb-0 voucher-detail-table`

---

### `financial\vouchers\index_v2.html`

**Table标签样式类**:
- `voucher-table`

---

### `financial\vouchers\pending_stock_ins.html`

**Table标签样式类**:
- `uf-table`

---

### `financial\vouchers\print.html`

**Table标签样式类**:
- `voucher-table`

**Th标签样式类**:
- `col-sequence`
- `col-summary`
- `col-subject`
- `col-debit`
- `col-credit`

---

### `financial\vouchers\view.html`

**Table标签样式类**:
- `finance-plus-table`

---

### `food_sample\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `food_sample\print.html`

**Table标签样式类**:
- `info-table`
- `info-table`

---

### `food_sample\print_daily.html`

**Th标签样式类**:
- `w-15`
- `w-25`
- `w-15`
- `w-15`
- `w-15`

---

### `food_sample\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `food_trace\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `food_trace\view.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-sm`
- `table table-bordered table-sm`
- `table table-bordered table-striped`
- `table table-bordered table-sm`
- `table table-bordered table-sm`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `ingredient\categories.html`

**Table标签样式类**:
- `table table-hover mb-0`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `text-center`

---

### `ingredient\index.html`

**Table标签样式类**:
- `enterprise-table`

**Th标签样式类**:
- `sortable number-col`
- `text-center`
- `sortable`
- `sortable`
- `number-col sortable`
- `status-col`
- `action-col`

---

### `ingredient\turnover.html`

**Table标签样式类**:
- `table table-compact table-hover table-bordered`
- `table table-compact table-hover table-bordered`
- `table table-compact table-hover table-bordered`
- `table table-compact table-hover table-bordered`

**Th标签样式类**:
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`
- `w-15`

---

### `ingredient\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `inspection\direct_index.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

---

### `inspection\edit.html`

**Table标签样式类**:
- `table table-bordered`

---

### `inspection\index.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

---

### `inspection\simplified_index.html`

**Table标签样式类**:
- `table table-bordered`

---

### `inspection\view.html`

**Table标签样式类**:
- `table table-bordered`

---

### `inventory\detail.html`

**Table标签样式类**:
- `table table-compact table-bordered table-striped`
- `table table-compact table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-30`
- `w-30`

---

### `inventory\expiry.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`

---

### `inventory\index.html`

**Table标签样式类**:
- `table table-compact table-hover table-bordered`

---

### `inventory\ingredient.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-sm`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `inventory\print_inventory.html`

**Table标签样式类**:
- `info-table`
- `items-table`

---

### `inventory\statistics.html`

**Table标签样式类**:
- `table table-striped table-hover`

**Thead标签样式类**:
- `table-dark`

**Th标签样式类**:
- `table-dark`

---

### `inventory\summary.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `inventory_alert\batch_create_requisition.html`

**Table标签样式类**:
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-25`
- `w-15`
- `w-15`
- `w-20`
- `w-15`

---

### `inventory_alert\check.html`

**Table标签样式类**:
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-sm`
- `table table-bordered table-striped`
- `table table-bordered table-sm`

---

### `inventory_alert\create_requisition.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `inventory_alert\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `inventory_alert\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `main\dashboard.html`

**Table标签样式类**:
- `enterprise-table`

**Th标签样式类**:
- `sortable`
- `sortable`
- `amount-col sortable`
- `status-col`
- `date-col sortable`
- `action-col`

---

### `main\food_samples.html`

**Table标签样式类**:
- `table table-hover`

---

### `main\purchase_orders.html`

**Table标签样式类**:
- `table table-hover`

---

### `main\suppliers.html`

**Table标签样式类**:
- `table table-hover`

---

### `material_batch\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `material_batch\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `menu_sync\index.html`

**Table标签样式类**:
- `table table-bordered table-hover`
- `table table-bordered table-hover`

---

### `product_batch\adjust_products.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `product_batch\approve.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `product_batch\confirm.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `product_batch\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `product_batch\view.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `purchase_order\create_from_menu.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-hover mb-0`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`

---

### `purchase_order\index.html`

**Table标签样式类**:
- `table table-hover table-compact`

**Th标签样式类**:
- `order-number-column`
- `datetime-column`
- `datetime-column`
- `amount-column`
- `status-column`
- `action-column`

---

### `purchase_order\print.html`

**Table标签样式类**:
- `info-table`
- `items-table`

**Th标签样式类**:
- `w-35`
- `w-15`
- `w-15`
- `w-25`

---

### `purchase_order\view.html`

**Table标签样式类**:
- `table table-hover table-compact`

**Th标签样式类**:
- `sequence-column`
- `ingredient-name-column`
- `quantity-column`
- `unit-column`
- `supplier-column`
- `status-info-column`

---

### `recipe\categories.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `school_admin\users.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `stock_in\batch_editor.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light text-danger`
- `bg-light text-danger`

---

### `stock_in\batch_editor_simplified.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Thead标签样式类**:
- `thead-dark`

**Th标签样式类**:
- `thead-dark`
- `text-center`

---

### `stock_in\batch_editor_simplified_scripts.html`

---

### `stock_in\batch_editor_step1.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light text-danger`
- `bg-light text-danger`

---

### `stock_in\batch_editor_step2.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light`
- `bg-light`
- `bg-light`

---

### `stock_in\by_ingredient.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `stock_in\confirm.html`

**Table标签样式类**:
- `table table-bordered`

---

### `stock_in\create_from_purchase.html`

**Table标签样式类**:
- `table table-bordered`

---

### `stock_in\create_from_purchase_order.html`

**Table标签样式类**:
- `table table-bordered table-items`

**Th标签样式类**:
- `w-20`
- `w-15`
- `w-15`
- `w-15`
- `w-20`

---

### `stock_in\edit.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-hover table-sm`
- `table table-bordered table-hover`
- `table table-bordered table-hover`
- `table table-bordered table-hover`

**Thead标签样式类**:
- `thead-light`
- `thead-light`
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `w-30`
- `bg-light text-primary`
- `bg-light text-danger`
- `thead-light`
- `thead-light`
- `bg-light text-primary`
- `bg-light text-danger`
- `thead-light`
- `thead-light`

---

### `stock_in\form.html`

**Table标签样式类**:
- `table table-bordered table-hover`
- `table table-sm`
- `table table-sm`
- `table table-bordered table-sm`

**Thead标签样式类**:
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `thead-light`

---

### `stock_in\index.html`

**Table标签样式类**:
- `table table-compact table-hover table-bordered`

---

### `stock_in\print.html`

**Table标签样式类**:
- `info-table`
- `items-table`

**Th标签样式类**:
- `w-15`
- `w-15`

---

### `stock_in\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped stock-in-items-table`
- `table table-bordered table-hover document-table`
- `table table-bordered table-hover`
- `table table-bordered table-hover`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-15`
- `thead-light`

---

### `stock_in\wizard.html`

**Table标签样式类**:
- `table table-bordered summary-table`
- `table table-bordered table-striped`
- `table table-striped table-hover`
- `table table-hover`

---

### `stock_in\wizard_simple.html`

**Table标签样式类**:
- `table table-striped`

---

### `stock_in_detail\view.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `stock_out\edit.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `stock_out\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `stock_out\print.html`

**Table标签样式类**:
- `info-table`
- `items-table`

---

### `stock_out\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-sm table-bordered`
- `table table-bordered table-striped`
- `info-table`
- `items-table`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-30`
- `w-30`
- `w-30`
- `w-30`
- `w-30`
- `w-30`

---

### `stock_out_item\detail.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`
- `table table-bordered`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-30`
- `w-30`

---

### `storage_location\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `supplier\category_index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `supplier\certificate_index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `supplier\certificate_view.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-30`

---

### `supplier\form.html`

**Table标签样式类**:
- `table table-sm table-bordered`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`

---

### `supplier\index.html`

**Table标签样式类**:
- `enterprise-table`

**Th标签样式类**:
- `sortable number-col`
- `sortable`
- `sortable`
- `text-center sortable`
- `status-col`
- `action-col`

---

### `supplier\product_index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

**Th标签样式类**:
- `text-center`

---

### `supplier\product_view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `supplier\school_index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `supplier\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `enterprise-table`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`
- `sortable`
- `sortable`
- `amount-col sortable`
- `status-col`
- `status-col`
- `action-col`

---

### `system_fix\permission_migration.html`

**Table标签样式类**:
- `table table-striped table-hover`

---

### `templates\stock_in\batch_editor.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light text-danger`
- `bg-light text-danger`

---

### `templates\stock_in\batch_editor_simplified.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Thead标签样式类**:
- `thead-dark`

**Th标签样式类**:
- `thead-dark`
- `text-center`

---

### `templates\stock_in\batch_editor_simplified_scripts.html`

---

### `templates\stock_in\batch_editor_step1.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light text-danger`
- `bg-light text-danger`

---

### `templates\stock_in\batch_editor_step2.html`

**Table标签样式类**:
- `table table-bordered batch-table`

**Th标签样式类**:
- `bg-light`
- `bg-light`
- `bg-light`

---

### `templates\stock_in\by_ingredient.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `templates\stock_in\confirm.html`

**Table标签样式类**:
- `table table-bordered`

---

### `templates\stock_in\create_from_purchase.html`

**Table标签样式类**:
- `table table-bordered`

---

### `templates\stock_in\create_from_purchase_order.html`

**Table标签样式类**:
- `table table-bordered table-items`

**Th标签样式类**:
- `w-20`
- `w-15`
- `w-15`
- `w-15`
- `w-20`

---

### `templates\stock_in\edit.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-hover table-sm`
- `table table-bordered table-hover`
- `table table-bordered table-hover`
- `table table-bordered table-hover`

**Thead标签样式类**:
- `thead-light`
- `thead-light`
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `w-30`
- `bg-light text-primary`
- `bg-light text-danger`
- `thead-light`
- `thead-light`
- `bg-light text-primary`
- `bg-light text-danger`
- `thead-light`
- `thead-light`

---

### `templates\stock_in\form.html`

**Table标签样式类**:
- `table table-bordered table-hover`
- `table table-sm`
- `table table-sm`
- `table table-bordered table-sm`

**Thead标签样式类**:
- `thead-light`
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `thead-light`

---

### `templates\stock_in\index.html`

**Table标签样式类**:
- `table table-compact table-hover table-bordered`

---

### `templates\stock_in\print.html`

**Table标签样式类**:
- `info-table`
- `items-table`

**Th标签样式类**:
- `w-15`
- `w-15`

---

### `templates\stock_in\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped stock-in-items-table`
- `table table-bordered table-hover document-table`
- `table table-bordered table-hover`
- `table table-bordered table-hover`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-15`
- `thead-light`

---

### `templates\stock_in\wizard.html`

**Table标签样式类**:
- `table table-bordered summary-table`
- `table table-bordered table-striped`
- `table table-striped table-hover`
- `table table-hover`

---

### `templates\stock_in\wizard_simple.html`

**Table标签样式类**:
- `table table-striped`

---

### `trace_document\upload.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-30`

---

### `traceability\batch_trace.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`
- `table table-bordered table-striped`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `traceability\ingredient_trace.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`

---

### `traceability\trace_interface.html`

**Table标签样式类**:
- `trace-table`

---

### `traceability\trace_interface_new.html`

**Table标签样式类**:
- `trace-table`
- `trace-table`
- `trace-table`
- `trace-table`

---

### `warehouse\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `warehouse\view.html`

**Table标签样式类**:
- `table table-bordered`
- `table table-bordered`
- `table table-bordered table-striped`

**Th标签样式类**:
- `w-30`
- `w-30`

---

### `warehouse_new\index.html`

**Table标签样式类**:
- `table table-bordered table-striped`

---

### `weekly_menu\1.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`

---

### `weekly_menu\index_v2.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `id-column`
- `week-column`
- `status-column`
- `creator-column`
- `time-column`
- `actions-column`

---

### `weekly_menu\plan.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`
- `w-30`
- `w-30`
- `w-30`

---

### `weekly_menu\plan_improved.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`

---

### `weekly_menu\plan_time_aware.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`
- `w-30`
- `w-30`
- `w-30`

---

### `weekly_menu\plan_v2.html`

**Table标签样式类**:
- `action-buttons-table`
- `menu-table`

---

### `weekly_menu\print.html`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-30`

---

### `weekly_menu\print_v2.html`

**Table标签样式类**:
- `menu-table`

**Th标签样式类**:
- `date-col`
- `meal-col`
- `meal-col`
- `meal-col`

---

### `weekly_menu\view.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`
- `w-30`
- `w-30`
- `w-30`

---

### `weekly_menu\view_v2.html`

**Table标签样式类**:
- `menu-table`

**Th标签样式类**:
- `date-col`
- `meal-col`
- `meal-col`
- `meal-col`

---

### `weekly_menu\weekly_menu(new)\index.html`

**Table标签样式类**:
- `table table-hover`

---

### `weekly_menu\weekly_menu(new)\plan_improved.html`

**Table标签样式类**:
- `table table-bordered`

**Th标签样式类**:
- `w-15`

---

### `weekly_menu\weekly_menu(new)\print.html`

**Th标签样式类**:
- `w-30`
- `w-30`
- `w-30`

---

### `weekly_menu\weekly_menu(new)\view.html`

**Table标签样式类**:
- `table table-hover menu-table mb-0`

**Thead标签样式类**:
- `thead-light`

**Th标签样式类**:
- `thead-light`
- `w-15`
- `w-30`
- `w-30`
- `w-30`

---

## 🏷️ 按样式类分组的使用统计

### Table标签样式类

#### `action-buttons-table` (使用 1 次)
- weekly_menu\plan_v2.html

#### `enterprise-table` (使用 4 次)
- ingredient\index.html
- main\dashboard.html
- supplier\index.html
- supplier\view.html

#### `finance-plus-table` (使用 1 次)
- financial\vouchers\view.html

#### `info-table` (使用 11 次)
- consumption_plan\view.html
- daily_management\print_inspection_photo_detail.html
- food_sample\print.html
- inventory\print_inventory.html
- purchase_order\print.html
- stock_in\print.html
- stock_out\print.html
- stock_out\view.html
- templates\stock_in\print.html

#### `inspection-table` (使用 6 次)
- daily_management\inspection_table_html.html
- daily_management\inspections_card_layout.html
- daily_management\inspections_simple_table.html
- daily_management\inspections_table.html

#### `items-table` (使用 7 次)
- consumption_plan\view.html
- inventory\print_inventory.html
- purchase_order\print.html
- stock_in\print.html
- stock_out\print.html
- stock_out\view.html
- templates\stock_in\print.html

#### `menu-table` (使用 3 次)
- weekly_menu\plan_v2.html
- weekly_menu\print_v2.html
- weekly_menu\view_v2.html

#### `preview-table` (使用 1 次)
- financial\assistant\quick_create.html

#### `table` (使用 14 次)
- daily_management\print_log.html
- daily_management\print_training.html

#### `table financial-table` (使用 1 次)
- financial\reports\trial_balance.html

#### `table table-bordered` (使用 99 次)
- admin\view_role.html
- batch_flow\form.html
- consultation\detail.html
- consumption_plan\create.html
- consumption_plan\create_from_weekly.html
- daily_management\companions.html
- daily_management\events.html
- daily_management\index.html
- daily_management\issues.html
- daily_management\logs.html
- daily_management\optimized_dashboard.html
- daily_management\print\print_companions.html
- daily_management\print\print_events.html
- daily_management\print\print_inspections.html
- daily_management\print\print_issues.html
- daily_management\print\print_log.html
- daily_management\print\print_trainings.html
- daily_management\simplified_inspection.html
- daily_management\trainings.html
- daily_management\view_issue.html
- daily_management\view_training.html
- financial\reports\voucher_summary.html
- food_sample\view.html
- ingredient\view.html
- inspection\direct_index.html
- inspection\edit.html
- inspection\index.html
- inspection\simplified_index.html
- inspection\view.html
- inventory\detail.html
- inventory\ingredient.html
- inventory_alert\create_requisition.html
- inventory_alert\view.html
- material_batch\view.html
- purchase_order\create_from_menu.html
- stock_in\by_ingredient.html
- stock_in\confirm.html
- stock_in\create_from_purchase.html
- stock_in\edit.html
- stock_in\view.html
- stock_out\edit.html
- stock_out\view.html
- stock_out_item\detail.html
- storage_location\view.html
- supplier\certificate_view.html
- supplier\product_view.html
- supplier\view.html
- templates\stock_in\by_ingredient.html
- templates\stock_in\confirm.html
- templates\stock_in\create_from_purchase.html
- templates\stock_in\edit.html
- templates\stock_in\view.html
- trace_document\upload.html
- traceability\batch_trace.html
- traceability\ingredient_trace.html
- warehouse\view.html
- weekly_menu\1.html
- weekly_menu\index_v2.html
- weekly_menu\plan.html
- weekly_menu\plan_improved.html
- weekly_menu\plan_time_aware.html
- weekly_menu\view.html
- weekly_menu\weekly_menu(new)\plan_improved.html

#### `table table-bordered batch-table` (使用 8 次)
- stock_in\batch_editor.html
- stock_in\batch_editor_simplified.html
- stock_in\batch_editor_step1.html
- stock_in\batch_editor_step2.html
- templates\stock_in\batch_editor.html
- templates\stock_in\batch_editor_simplified.html
- templates\stock_in\batch_editor_step1.html
- templates\stock_in\batch_editor_step2.html

#### `table table-bordered inspection-table` (使用 3 次)
- daily_management\inspections.html

#### `table table-bordered summary-table` (使用 2 次)
- stock_in\wizard.html
- templates\stock_in\wizard.html

#### `table table-bordered table-hover` (使用 16 次)
- admin\system\module_visibility.html
- consumption_plan\super_editor.html
- menu_sync\index.html
- stock_in\edit.html
- stock_in\form.html
- stock_in\view.html
- templates\stock_in\edit.html
- templates\stock_in\form.html
- templates\stock_in\view.html

#### `table table-bordered table-hover document-table` (使用 2 次)
- stock_in\view.html
- templates\stock_in\view.html

#### `table table-bordered table-hover mb-0` (使用 2 次)
- admin\view_user.html
- purchase_order\create_from_menu.html

#### `table table-bordered table-items` (使用 2 次)
- stock_in\create_from_purchase_order.html
- templates\stock_in\create_from_purchase_order.html

#### `table table-bordered table-sm` (使用 10 次)
- admin\view_role.html
- food_trace\view.html
- inventory\ingredient.html
- inventory_alert\check.html
- stock_in\form.html
- templates\stock_in\form.html

#### `table table-bordered table-striped` (使用 81 次)
- admin\super_delete\index.html
- components\page_layout.html
- consultation\list.html
- consumption_plan\create.html
- consumption_plan\edit.html
- consumption_plan\new.html
- daily_management\inspections_new.html
- data_repair\index.html
- financial\reports\voucher_summary.html
- food_sample\index.html
- food_trace\index.html
- food_trace\view.html
- ingredient\view.html
- inventory\detail.html
- inventory\expiry.html
- inventory\ingredient.html
- inventory\summary.html
- inventory_alert\batch_create_requisition.html
- inventory_alert\check.html
- inventory_alert\index.html
- material_batch\index.html
- material_batch\view.html
- product_batch\adjust_products.html
- product_batch\approve.html
- product_batch\confirm.html
- product_batch\index.html
- product_batch\view.html
- recipe\categories.html
- school_admin\users.html
- stock_in\by_ingredient.html
- stock_in\edit.html
- stock_in\wizard.html
- stock_in_detail\view.html
- stock_out\edit.html
- stock_out\index.html
- stock_out\view.html
- storage_location\view.html
- supplier\category_index.html
- supplier\certificate_index.html
- supplier\product_index.html
- supplier\product_view.html
- supplier\school_index.html
- supplier\view.html
- templates\stock_in\by_ingredient.html
- templates\stock_in\edit.html
- templates\stock_in\wizard.html
- traceability\batch_trace.html
- traceability\ingredient_trace.html
- warehouse\index.html
- warehouse\view.html
- warehouse_new\index.html

#### `table table-bordered table-striped stock-in-items-table` (使用 2 次)
- stock_in\view.html
- templates\stock_in\view.html

#### `table table-bordered voucher-editor-table` (使用 1 次)
- financial\vouchers\edit_professional.html

#### `table table-compact table-bordered` (使用 1 次)
- inventory\detail.html

#### `table table-compact table-bordered table-striped` (使用 1 次)
- inventory\detail.html

#### `table table-compact table-hover table-bordered` (使用 8 次)
- consumption_plan\index.html
- ingredient\turnover.html
- inventory\index.html
- stock_in\index.html
- templates\stock_in\index.html

#### `table table-hover` (使用 23 次)
- admin\roles.html
- admin\users.html
- admin\view_role.html
- admin\view_user.html
- area\index.html
- employee\daily_health_check.html
- employee\health_certificates.html
- employee\index.html
- employee\view_employee.html
- main\food_samples.html
- main\purchase_orders.html
- main\suppliers.html
- stock_in\wizard.html
- templates\stock_in\wizard.html
- weekly_menu\weekly_menu(new)\index.html

#### `table table-hover backup-table` (使用 1 次)
- admin\system\backups.html

#### `table table-hover mb-0` (使用 1 次)
- ingredient\categories.html

#### `table table-hover menu-table mb-0` (使用 1 次)
- weekly_menu\weekly_menu(new)\view.html

#### `table table-hover table-compact` (使用 2 次)
- purchase_order\index.html
- purchase_order\view.html

#### `table table-hover table-sm` (使用 2 次)
- stock_in\edit.html
- templates\stock_in\edit.html

#### `table table-hover table-striped` (使用 3 次)
- area\view_area.html

#### `table table-sm` (使用 12 次)
- admin\guide_management\scenarios.html
- admin\view_user.html
- area\index.html
- employee\view_employee.html
- financial\assistant\quick_create.html
- stock_in\form.html
- templates\stock_in\form.html

#### `table table-sm mb-0 voucher-detail-table` (使用 1 次)
- financial\vouchers\index.html

#### `table table-sm system-info` (使用 1 次)
- admin\system\monitor.html

#### `table table-sm table-bordered` (使用 2 次)
- stock_out\view.html
- supplier\form.html

#### `table table-sm table-hover` (使用 2 次)
- admin\system\monitor.html

#### `table table-striped` (使用 5 次)
- admin\guide_management\demo_data.html
- admin\guide_management\users.html
- financial\assistant\index.html
- stock_in\wizard_simple.html
- templates\stock_in\wizard_simple.html

#### `table table-striped table-hover` (使用 5 次)
- admin\carousel_list.html
- inventory\statistics.html
- stock_in\wizard.html
- system_fix\permission_migration.html
- templates\stock_in\wizard.html

#### `trace-table` (使用 5 次)
- traceability\trace_interface.html
- traceability\trace_interface_new.html

#### `uf-financial-table` (使用 1 次)
- financial\payables\pending_stock_ins.html

#### `uf-table` (使用 10 次)
- financial\accounting_subjects\index.html
- financial\payables\index.html
- financial\payments\index.html
- financial\reports\balance_sheet.html
- financial\reports\cost_analysis.html
- financial\reports\payables_aging.html
- financial\vouchers\pending_stock_ins.html

#### `uf-table uf-balance-sheet-table` (使用 1 次)
- financial\ledgers\balance.html

#### `uf-table uf-cost-analysis-table` (使用 1 次)
- financial\reports\income_statement.html

#### `uf-table uf-general-ledger-table` (使用 1 次)
- financial\ledgers\general.html

#### `uf-table uf-generated-ledgers-table` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-table uf-ledger-table` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-table uf-subject-table` (使用 1 次)
- financial\vouchers\create.html

#### `uf-table uf-summary-table` (使用 1 次)
- financial\ledgers\general.html

#### `uf-table uf-voucher-table` (使用 1 次)
- financial\vouchers\create.html

#### `voucher-table` (使用 3 次)
- financial\vouchers\edit.html
- financial\vouchers\index_v2.html
- financial\vouchers\print.html

### Thead标签样式类

#### `table-dark` (使用 2 次)
- admin\carousel_list.html
- inventory\statistics.html

#### `thead-dark` (使用 3 次)
- financial\vouchers\edit_professional.html
- stock_in\batch_editor_simplified.html
- templates\stock_in\batch_editor_simplified.html

#### `thead-light` (使用 27 次)
- admin\system\module_visibility.html
- admin\view_user.html
- area\view_area.html
- consumption_plan\create_from_weekly.html
- financial\reports\voucher_summary.html
- ingredient\categories.html
- purchase_order\create_from_menu.html
- stock_in\edit.html
- stock_in\form.html
- stock_in\view.html
- supplier\form.html
- templates\stock_in\edit.html
- templates\stock_in\form.html
- templates\stock_in\view.html
- weekly_menu\weekly_menu(new)\view.html

### Th标签样式类

#### `action-col` (使用 4 次)
- ingredient\index.html
- main\dashboard.html
- supplier\index.html
- supplier\view.html

#### `action-column` (使用 1 次)
- purchase_order\index.html

#### `actions-column` (使用 1 次)
- weekly_menu\index_v2.html

#### `amount-col sortable` (使用 2 次)
- main\dashboard.html
- supplier\view.html

#### `amount-column` (使用 1 次)
- purchase_order\index.html

#### `bg-light` (使用 6 次)
- stock_in\batch_editor_step2.html
- templates\stock_in\batch_editor_step2.html

#### `bg-light text-danger` (使用 12 次)
- stock_in\batch_editor.html
- stock_in\batch_editor_step1.html
- stock_in\edit.html
- templates\stock_in\batch_editor.html
- templates\stock_in\batch_editor_step1.html
- templates\stock_in\edit.html

#### `bg-light text-primary` (使用 4 次)
- stock_in\edit.html
- templates\stock_in\edit.html

#### `col-actions` (使用 1 次)
- financial\payables\pending_stock_ins.html

#### `col-amount` (使用 1 次)
- financial\payables\pending_stock_ins.html

#### `col-credit` (使用 1 次)
- financial\vouchers\print.html

#### `col-date` (使用 2 次)
- financial\payables\pending_stock_ins.html

#### `col-debit` (使用 1 次)
- financial\vouchers\print.html

#### `col-sequence` (使用 1 次)
- financial\vouchers\print.html

#### `col-subject` (使用 1 次)
- financial\vouchers\print.html

#### `col-subject-name` (使用 1 次)
- financial\payables\pending_stock_ins.html

#### `col-summary` (使用 1 次)
- financial\vouchers\print.html

#### `col-voucher` (使用 2 次)
- financial\payables\pending_stock_ins.html

#### `creator-column` (使用 1 次)
- weekly_menu\index_v2.html

#### `date-col` (使用 2 次)
- weekly_menu\print_v2.html
- weekly_menu\view_v2.html

#### `date-col sortable` (使用 1 次)
- main\dashboard.html

#### `datetime-column` (使用 2 次)
- purchase_order\index.html

#### `id-column` (使用 1 次)
- weekly_menu\index_v2.html

#### `ingredient-name-column` (使用 1 次)
- purchase_order\view.html

#### `item-header` (使用 6 次)
- daily_management\inspections_simple_table.html

#### `meal-col` (使用 6 次)
- weekly_menu\print_v2.html
- weekly_menu\view_v2.html

#### `number-col sortable` (使用 1 次)
- ingredient\index.html

#### `order-number-column` (使用 1 次)
- purchase_order\index.html

#### `quantity-column` (使用 1 次)
- purchase_order\view.html

#### `sequence-column` (使用 1 次)
- purchase_order\view.html

#### `sortable` (使用 8 次)
- ingredient\index.html
- main\dashboard.html
- supplier\index.html
- supplier\view.html

#### `sortable number-col` (使用 2 次)
- ingredient\index.html
- supplier\index.html

#### `status-col` (使用 5 次)
- ingredient\index.html
- main\dashboard.html
- supplier\index.html
- supplier\view.html

#### `status-column` (使用 2 次)
- purchase_order\index.html
- weekly_menu\index_v2.html

#### `status-info-column` (使用 1 次)
- purchase_order\view.html

#### `supplier-column` (使用 1 次)
- purchase_order\view.html

#### `table-dark` (使用 2 次)
- admin\carousel_list.html
- inventory\statistics.html

#### `text-center` (使用 6 次)
- financial\ledgers\general.html
- ingredient\categories.html
- ingredient\index.html
- stock_in\batch_editor_simplified.html
- supplier\product_index.html
- templates\stock_in\batch_editor_simplified.html

#### `text-center sortable` (使用 1 次)
- supplier\index.html

#### `text-right` (使用 15 次)
- financial\reports\trial_balance.html
- financial\reports\voucher_summary.html

#### `thead-dark` (使用 3 次)
- financial\vouchers\edit_professional.html
- stock_in\batch_editor_simplified.html
- templates\stock_in\batch_editor_simplified.html

#### `thead-light` (使用 27 次)
- admin\system\module_visibility.html
- admin\view_user.html
- area\view_area.html
- consumption_plan\create_from_weekly.html
- financial\reports\voucher_summary.html
- ingredient\categories.html
- purchase_order\create_from_menu.html
- stock_in\edit.html
- stock_in\form.html
- stock_in\view.html
- supplier\form.html
- templates\stock_in\edit.html
- templates\stock_in\form.html
- templates\stock_in\view.html
- weekly_menu\weekly_menu(new)\view.html

#### `time-column` (使用 1 次)
- weekly_menu\index_v2.html

#### `time-header` (使用 1 次)
- daily_management\inspections_simple_table.html

#### `uf-amount-col` (使用 15 次)
- financial\ledgers\balance.html
- financial\ledgers\general.html
- financial\reports\income_statement.html

#### `uf-col-balance sortable` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-checkbox` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-code` (使用 1 次)
- financial\vouchers\create.html

#### `uf-col-code sortable` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-credit` (使用 2 次)
- financial\ledgers\detail.html
- financial\vouchers\create.html

#### `uf-col-credit sortable` (使用 2 次)
- financial\ledgers\detail.html
- financial\ledgers\general.html

#### `uf-col-date sortable` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-debit` (使用 2 次)
- financial\ledgers\detail.html
- financial\vouchers\create.html

#### `uf-col-debit sortable` (使用 2 次)
- financial\ledgers\detail.html
- financial\ledgers\general.html

#### `uf-col-direction` (使用 2 次)
- financial\ledgers\general.html
- financial\vouchers\create.html

#### `uf-col-ending` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-ending sortable` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-generated` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-level` (使用 1 次)
- financial\vouchers\create.html

#### `uf-col-line-no` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-name` (使用 1 次)
- financial\vouchers\create.html

#### `uf-col-name sortable` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-opening` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-opening sortable` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-operations` (使用 4 次)
- financial\ledgers\detail.html
- financial\ledgers\general.html
- financial\vouchers\create.html

#### `uf-col-period` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-records` (使用 1 次)
- financial\ledgers\detail.html

#### `uf-col-sequence` (使用 1 次)
- financial\vouchers\create.html

#### `uf-col-subject` (使用 2 次)
- financial\ledgers\detail.html
- financial\vouchers\create.html

#### `uf-col-summary` (使用 2 次)
- financial\ledgers\detail.html
- financial\vouchers\create.html

#### `uf-col-type` (使用 2 次)
- financial\ledgers\detail.html
- financial\vouchers\create.html

#### `uf-col-type sortable` (使用 1 次)
- financial\ledgers\general.html

#### `uf-col-voucher sortable` (使用 1 次)
- financial\ledgers\detail.html

#### `unit-column` (使用 1 次)
- purchase_order\view.html

#### `w-15` (使用 43 次)
- consumption_plan\super_editor.html
- food_sample\print_daily.html
- ingredient\turnover.html
- inventory_alert\batch_create_requisition.html
- purchase_order\print.html
- stock_in\create_from_purchase_order.html
- stock_in\print.html
- stock_in\view.html
- templates\stock_in\create_from_purchase_order.html
- templates\stock_in\print.html
- templates\stock_in\view.html
- weekly_menu\1.html
- weekly_menu\plan.html
- weekly_menu\plan_improved.html
- weekly_menu\plan_time_aware.html
- weekly_menu\view.html
- weekly_menu\weekly_menu(new)\plan_improved.html
- weekly_menu\weekly_menu(new)\view.html

#### `w-20` (使用 5 次)
- inventory_alert\batch_create_requisition.html
- stock_in\create_from_purchase_order.html
- templates\stock_in\create_from_purchase_order.html

#### `w-25` (使用 3 次)
- food_sample\print_daily.html
- inventory_alert\batch_create_requisition.html
- purchase_order\print.html

#### `w-30` (使用 75 次)
- admin\system\module_visibility.html
- admin\view_role.html
- batch_flow\form.html
- consumption_plan\create.html
- daily_management\view_issue.html
- daily_management\view_training.html
- food_sample\view.html
- ingredient\view.html
- inventory\detail.html
- inventory\ingredient.html
- inventory_alert\create_requisition.html
- inventory_alert\view.html
- material_batch\view.html
- stock_in\by_ingredient.html
- stock_in\edit.html
- stock_in\view.html
- stock_out\edit.html
- stock_out\view.html
- stock_out_item\detail.html
- storage_location\view.html
- supplier\certificate_view.html
- supplier\product_view.html
- supplier\view.html
- templates\stock_in\by_ingredient.html
- templates\stock_in\edit.html
- templates\stock_in\view.html
- trace_document\upload.html
- traceability\batch_trace.html
- traceability\ingredient_trace.html
- warehouse\view.html
- weekly_menu\plan.html
- weekly_menu\plan_time_aware.html
- weekly_menu\print.html
- weekly_menu\view.html
- weekly_menu\weekly_menu(new)\print.html
- weekly_menu\weekly_menu(new)\view.html

#### `w-35` (使用 1 次)
- purchase_order\print.html

#### `w-60` (使用 1 次)
- admin\system\module_visibility.html

#### `week-column` (使用 1 次)
- weekly_menu\index_v2.html
