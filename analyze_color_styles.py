#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
颜色样式分析工具
从表头样式类扫描报告中提取所有样式类，并在CSS和HTML文件中查找涉及颜色处理的样式
"""

import os
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

def extract_styles_from_report():
    """从报告中提取所有样式类"""
    
    try:
        with open("表头样式类扫描报告.md", 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取报告文件失败: {e}")
        return set()
    
    # 提取所有样式类名
    style_pattern = r'#### `([^`]+)`'
    styles = set()
    
    for match in re.finditer(style_pattern, content):
        style_name = match.group(1)
        styles.add(style_name)
    
    return styles

def find_color_properties_in_css():
    """在CSS文件中查找颜色相关属性"""
    
    color_properties = [
        'color', 'background-color', 'background', 'border-color', 
        'box-shadow', 'text-shadow', 'outline-color', 'fill', 'stroke'
    ]
    
    css_files = []
    
    # 查找所有CSS文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.css'):
                css_files.append(os.path.join(root, file))
    
    color_styles = {}
    
    for css_file in css_files:
        try:
            with open(css_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取CSS文件失败 {css_file}: {e}")
            continue
        
        # 解析CSS规则
        css_rules = parse_css_rules(content)
        
        for selector, properties in css_rules.items():
            # 检查是否包含颜色属性
            color_props = {}
            for prop, value in properties.items():
                if any(color_prop in prop.lower() for color_prop in color_properties):
                    color_props[prop] = value
                elif is_color_value(value):
                    color_props[prop] = value
            
            if color_props:
                if css_file not in color_styles:
                    color_styles[css_file] = {}
                color_styles[css_file][selector] = color_props
    
    return color_styles

def parse_css_rules(css_content):
    """解析CSS规则"""
    
    rules = {}
    
    # 移除注释
    css_content = re.sub(r'/\*.*?\*/', '', css_content, flags=re.DOTALL)
    
    # 匹配CSS规则
    rule_pattern = r'([^{}]+)\s*\{([^{}]*)\}'
    
    for match in re.finditer(rule_pattern, css_content):
        selectors = match.group(1).strip()
        properties_block = match.group(2).strip()
        
        # 解析属性
        properties = {}
        prop_pattern = r'([^:;]+):\s*([^;]+);?'
        
        for prop_match in re.finditer(prop_pattern, properties_block):
            prop_name = prop_match.group(1).strip()
            prop_value = prop_match.group(2).strip()
            properties[prop_name] = prop_value
        
        if properties:
            rules[selectors] = properties
    
    return rules

def is_color_value(value):
    """判断值是否为颜色值"""
    
    color_patterns = [
        r'#[0-9a-fA-F]{3,8}',           # 十六进制颜色
        r'rgb\s*\([^)]+\)',             # RGB颜色
        r'rgba\s*\([^)]+\)',            # RGBA颜色
        r'hsl\s*\([^)]+\)',             # HSL颜色
        r'hsla\s*\([^)]+\)',            # HSLA颜色
        r'var\s*\(\s*--[^)]+\)',        # CSS变量
    ]
    
    # 常见颜色名称
    color_names = [
        'red', 'blue', 'green', 'yellow', 'orange', 'purple', 'pink', 'brown',
        'black', 'white', 'gray', 'grey', 'transparent', 'inherit', 'initial',
        'currentColor', 'lightgray', 'darkgray', 'lightblue', 'darkblue'
    ]
    
    value_lower = value.lower()
    
    # 检查颜色模式
    for pattern in color_patterns:
        if re.search(pattern, value, re.IGNORECASE):
            return True
    
    # 检查颜色名称
    for color_name in color_names:
        if color_name in value_lower:
            return True
    
    return False

def find_inline_styles_in_html():
    """在HTML文件中查找内联样式中的颜色"""
    
    html_files = []
    
    # 查找所有HTML文件
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.html'):
                html_files.append(os.path.join(root, file))
    
    inline_color_styles = {}
    
    for html_file in html_files:
        try:
            with open(html_file, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取HTML文件失败 {html_file}: {e}")
            continue
        
        # 查找style标签中的CSS
        style_pattern = r'<style[^>]*>(.*?)</style>'
        style_matches = re.findall(style_pattern, content, re.DOTALL | re.IGNORECASE)
        
        for style_content in style_matches:
            css_rules = parse_css_rules(style_content)
            
            for selector, properties in css_rules.items():
                color_props = {}
                for prop, value in properties.items():
                    if ('color' in prop.lower() or 'background' in prop.lower() or 
                        'border' in prop.lower() or is_color_value(value)):
                        color_props[prop] = value
                
                if color_props:
                    if html_file not in inline_color_styles:
                        inline_color_styles[html_file] = {}
                    inline_color_styles[html_file][selector] = color_props
        
        # 查找内联style属性
        inline_pattern = r'style\s*=\s*["\']([^"\']*)["\']'
        inline_matches = re.findall(inline_pattern, content, re.IGNORECASE)
        
        for inline_style in inline_matches:
            properties = {}
            prop_pattern = r'([^:;]+):\s*([^;]+);?'
            
            for prop_match in re.finditer(prop_pattern, inline_style):
                prop_name = prop_match.group(1).strip()
                prop_value = prop_match.group(2).strip()
                
                if ('color' in prop_name.lower() or 'background' in prop_name.lower() or 
                    'border' in prop_name.lower() or is_color_value(prop_value)):
                    properties[prop_name] = prop_value
            
            if properties:
                if html_file not in inline_color_styles:
                    inline_color_styles[html_file] = {}
                selector_key = f"inline-style-{len(inline_color_styles[html_file])}"
                inline_color_styles[html_file][selector_key] = properties
    
    return inline_color_styles

def analyze_report_styles_colors(report_styles, css_color_styles, html_color_styles):
    """分析报告中的样式类是否涉及颜色处理"""
    
    results = {
        'styles_with_colors': {},
        'styles_without_colors': set(),
        'summary': {
            'total_styles': len(report_styles),
            'styles_with_color_count': 0,
            'styles_without_color_count': 0
        }
    }
    
    for style_name in report_styles:
        found_colors = {}
        
        # 在CSS文件中查找
        for css_file, selectors in css_color_styles.items():
            for selector, properties in selectors.items():
                # 检查选择器是否包含当前样式类
                if (f'.{style_name}' in selector or 
                    f' {style_name}' in selector or 
                    selector.endswith(style_name)):
                    
                    if css_file not in found_colors:
                        found_colors[css_file] = []
                    found_colors[css_file].append({
                        'selector': selector,
                        'properties': properties,
                        'type': 'css'
                    })
        
        # 在HTML文件中查找
        for html_file, selectors in html_color_styles.items():
            for selector, properties in selectors.items():
                if (f'.{style_name}' in selector or 
                    f' {style_name}' in selector or 
                    selector.endswith(style_name)):
                    
                    if html_file not in found_colors:
                        found_colors[html_file] = []
                    found_colors[html_file].append({
                        'selector': selector,
                        'properties': properties,
                        'type': 'html_style'
                    })
        
        if found_colors:
            results['styles_with_colors'][style_name] = found_colors
            results['summary']['styles_with_color_count'] += 1
        else:
            results['styles_without_colors'].add(style_name)
            results['summary']['styles_without_color_count'] += 1
    
    return results

def generate_color_analysis_report(report_styles, analysis_results):
    """生成颜色分析报告"""
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 表头样式类颜色处理分析报告",
        "",
        f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 分析概览",
        "",
        f"- **报告中样式类总数**: {analysis_results['summary']['total_styles']}",
        f"- **涉及颜色处理的样式类**: {analysis_results['summary']['styles_with_color_count']}",
        f"- **不涉及颜色处理的样式类**: {analysis_results['summary']['styles_without_color_count']}",
        f"- **颜色处理覆盖率**: {analysis_results['summary']['styles_with_color_count'] / analysis_results['summary']['total_styles'] * 100:.1f}%",
        "",
    ])
    
    # 涉及颜色处理的样式类
    if analysis_results['styles_with_colors']:
        report_lines.extend([
            "## 🎨 涉及颜色处理的样式类",
            "",
        ])
        
        for style_name in sorted(analysis_results['styles_with_colors'].keys()):
            color_info = analysis_results['styles_with_colors'][style_name]
            report_lines.append(f"### `{style_name}`")
            report_lines.append("")
            
            for file_path, definitions in color_info.items():
                report_lines.append(f"**文件**: `{file_path}`")
                report_lines.append("")
                
                for definition in definitions:
                    report_lines.append(f"**选择器**: `{definition['selector']}`")
                    report_lines.append(f"**类型**: {definition['type']}")
                    report_lines.append("**颜色属性**:")
                    
                    for prop, value in definition['properties'].items():
                        report_lines.append(f"- `{prop}: {value}`")
                    
                    report_lines.append("")
            
            report_lines.append("---")
            report_lines.append("")
    
    # 不涉及颜色处理的样式类
    if analysis_results['styles_without_colors']:
        report_lines.extend([
            "## ⚪ 不涉及颜色处理的样式类",
            "",
            "以下样式类在CSS和HTML文件中未找到颜色相关定义：",
            "",
        ])
        
        for style_name in sorted(analysis_results['styles_without_colors']):
            report_lines.append(f"- `{style_name}`")
        
        report_lines.append("")
    
    # 优化建议
    report_lines.extend([
        "## 💡 颜色处理优化建议",
        "",
        "### 🎯 统一颜色管理",
        "",
        "1. **使用CSS变量**: 将硬编码的颜色值替换为CSS变量",
        "2. **建立颜色系统**: 创建统一的颜色调色板",
        "3. **主题一致性**: 确保所有颜色符合系统主题设计",
        "",
        "### 🔧 具体优化方案",
        "",
        "1. **定义主题变量**: 如 `--primary-color`, `--secondary-color`, `--bg-light` 等",
        "2. **替换硬编码颜色**: 将 `#ffffff`, `rgb()` 等替换为变量",
        "3. **统一背景色**: 使用 `var(--theme-bg-hover)` 等系统变量",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🎨 开始分析表头样式类的颜色处理...")
    
    # 1. 从报告中提取样式类
    print("📋 提取报告中的样式类...")
    report_styles = extract_styles_from_report()
    print(f"   发现 {len(report_styles)} 个样式类")
    
    # 2. 在CSS文件中查找颜色样式
    print("🔍 扫描CSS文件中的颜色样式...")
    css_color_styles = find_color_properties_in_css()
    print(f"   扫描了 {len(css_color_styles)} 个CSS文件")
    
    # 3. 在HTML文件中查找内联颜色样式
    print("🔍 扫描HTML文件中的内联颜色样式...")
    html_color_styles = find_inline_styles_in_html()
    print(f"   扫描了 {len(html_color_styles)} 个HTML文件")
    
    # 4. 分析报告样式类的颜色处理
    print("📊 分析样式类的颜色处理...")
    analysis_results = analyze_report_styles_colors(report_styles, css_color_styles, html_color_styles)
    
    # 5. 生成报告
    print("📝 生成颜色分析报告...")
    report_content = generate_color_analysis_report(report_styles, analysis_results)
    
    # 6. 保存报告
    report_file = "表头样式类颜色处理分析报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 颜色分析报告已生成: {report_file}")
    print(f"🎨 {analysis_results['summary']['styles_with_color_count']} 个样式类涉及颜色处理")
    print(f"⚪ {analysis_results['summary']['styles_without_color_count']} 个样式类不涉及颜色处理")

if __name__ == "__main__":
    main()
