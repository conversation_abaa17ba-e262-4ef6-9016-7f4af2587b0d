{% extends 'base.html' %}

{% block title %}食材分类管理{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 页面标题区域 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2 class="mb-1">
                <i class="fas fa-sitemap text-primary mr-2"></i>食材分类管理
            </h2>
            <p class="text-muted mb-0">管理食材分类层级结构</p>
        </div>
        <div>
            <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#categoryModal" onclick="openAddModal()">
                <i class="fas fa-plus mr-1"></i>添加分类
            </button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
     
        <!-- 分类列表 -->
        <div class="col-lg-7 mb-4">
            <div class="card shadow-sm">
                         <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="thead-light">
                                <tr>
                                    <th style="width: 60px;">ID</th>
                                    <th>分类名称</th>
                                    <th>父分类</th>
                                    <th>描述</th>
                                    <th class="text-center" style="width: 120px;">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for category in categories %}
                                <tr>
                                    <td>
                                        <span class="badge badge-light">{{ category.id }}</span>
                                    </td>
                                    <td>
                                        <i class="fas fa-tag text-primary mr-2"></i>
                                        <strong>{{ category.name }}</strong>
                                    </td>
                                    <td>
                                        {% if category.parent %}
                                            <span class="badge badge-secondary">{{ category.parent.name }}</span>
                                        {% else %}
                                            <span class="text-muted">顶级分类</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="text-muted">{{ category.description or '暂无描述' }}</span>
                                    </td>
                                    <td class="text-center">
                                        <div class="btn-group btn-group-sm" role="group">
                                            <button type="button"
                                                    class="btn btn-outline-primary edit-btn"
                                                    data-id="{{ category.id }}"
                                                    data-name="{{ category.name }}"
                                                    data-parent-id="{{ category.parent_id or '' }}"
                                                    data-description="{{ category.description or '' }}"
                                                    title="编辑分类">
                                                <i class="fas fa-edit"></i>
                                            </button>
                                            <button class="btn btn-outline-danger delete-btn"
                                                    data-id="{{ category.id }}"
                                                    title="删除分类">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </div>
                                    </td>
                                </tr>
                                {% else %}
                                <tr>
                                    <td colspan="5" class="text-center py-4">
                                        <div class="text-muted">
                                            <i class="fas fa-inbox fa-2x mb-2 d-block"></i>
                                            <p class="mb-1">暂无分类数据</p>
                                            <small>点击上方"添加分类"按钮创建第一个分类</small>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 分类表单模态框 -->
<div class="modal fade" id="categoryModal" tabindex="-1" role="dialog" aria-labelledby="categoryModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="categoryModalLabel">
                    <i class="fas fa-plus mr-2"></i><span id="modalTitle">添加分类</span>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <form id="categoryForm">
                <div class="modal-body">
                    <input type="hidden" id="categoryId" name="id">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">

                    <div class="form-group">
                        <label for="categoryName">分类名称 <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="categoryName" name="name" required>
                        <div class="invalid-feedback"></div>
                    </div>

                    <div class="form-group">
                        <label for="parentId">父分类</label>
                        <select class="form-control" id="parentId" name="parent_id">
                            <option value="">-- 无父分类（顶级分类）--</option>
                            {% for category in categories %}
                            <option value="{{ category.id }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                        <small class="form-text text-muted">选择父分类可以创建子分类</small>
                    </div>

                    <div class="form-group">
                        <label for="categoryDescription">描述</label>
                        <textarea class="form-control" id="categoryDescription" name="description" rows="3" placeholder="请输入分类描述（可选）"></textarea>
                        <small class="form-text text-muted">简要描述该分类的用途或特点</small>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">
                        <i class="fas fa-times mr-1"></i>取消
                    </button>
                    <button type="submit" class="btn btn-primary" id="saveBtn">
                        <i class="fas fa-save mr-1"></i>保存
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteModal" tabindex="-1" role="dialog" aria-labelledby="deleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteModalLabel">
                    <i class="fas fa-exclamation-triangle mr-2"></i>确认删除
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body text-center py-4">
                <i class="fas fa-trash-alt fa-3x text-danger mb-3"></i>
                <h6 class="mb-2">确定要删除这个食材分类吗？</h6>
                <p class="text-muted mb-0">此操作不可逆，请谨慎操作。</p>
            </div>
            <div class="modal-footer justify-content-center">
                <button type="button" class="btn btn-secondary px-4" data-dismiss="modal">
                    <i class="fas fa-times mr-1"></i>取消
                </button>
                <button type="button" class="btn btn-danger px-4" id="confirmDelete">
                    <i class="fas fa-check mr-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<style>
/* 基于系统框架的简单样式增强 */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

.btn-group-sm .btn {
    padding: 0.25rem 0.5rem;
}

#category-tree {
    background-color: #f8f9fa;
    border-radius: 0.25rem;
    padding: 1rem;
}

.badge {
    font-size: 0.75rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
    .d-flex.justify-content-between {
        flex-direction: column;
        gap: 1rem;
    }

    .col-lg-5, .col-lg-7 {
        margin-bottom: 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }
}
</style>
{% endblock %}

{% block scripts %}
<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='vendor/jstree/jstree.min.js') }}"></script>
<link rel="stylesheet" href="{{ url_for('static', filename='vendor/jstree/themes/default/style.min.css') }}" />
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 初始化分类树
        var categoryTree = {{ category_tree|safe }};

        function transformData(data) {
            return data.map(function(item) {
                var node = {
                    id: item.id.toString(),
                    text: item.name,
                    state: { opened: true }
                };

                if (item.children && item.children.length > 0) {
                    node.children = transformData(item.children);
                }

                return node;
            });
        }

        $('#category-tree').jstree({
            'core': {
                'data': transformData(categoryTree)
            }
        });

        // 全局变量
        var deleteId = null;
        var isEditMode = false;

        // 打开添加模态框
        window.openAddModal = function() {
            isEditMode = false;
            $('#modalTitle').text('添加分类');
            $('#categoryForm')[0].reset();
            $('#categoryId').val('');
            $('#categoryModal .modal-header').removeClass('bg-warning').addClass('bg-primary');
            $('#categoryModal .modal-header i').removeClass('fa-edit').addClass('fa-plus');
            clearValidation();
            $('#categoryModal').modal('show');
        };

        // 编辑按钮点击事件
        $('.edit-btn').click(function() {
            isEditMode = true;
            var id = $(this).data('id');
            var name = $(this).data('name');
            var parentId = $(this).data('parent-id');
            var description = $(this).data('description');

            $('#modalTitle').text('编辑分类');
            $('#categoryId').val(id);
            $('#categoryName').val(name);
            $('#parentId').val(parentId);
            $('#categoryDescription').val(description);

            // 编辑模式时，不能选择自己作为父分类
            $('#parentId option').prop('disabled', false);
            $('#parentId option[value="' + id + '"]').prop('disabled', true);

            $('#categoryModal .modal-header').removeClass('bg-primary').addClass('bg-warning');
            $('#categoryModal .modal-header i').removeClass('fa-plus').addClass('fa-edit');
            clearValidation();
            $('#categoryModal').modal('show');
        });

        // 表单提交
        $('#categoryForm').submit(function(e) {
            e.preventDefault();

            var formData = new FormData(this);
            var url = isEditMode ?
                '{{ url_for("ingredient_category.edit", id=0) }}'.replace('0', $('#categoryId').val()) :
                '{{ url_for("ingredient_category.create") }}';

            // 显示加载状态
            var $saveBtn = $('#saveBtn');
            var originalText = $saveBtn.html();
            $saveBtn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>保存中...');

            $.ajax({
                url: url,
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // 如果返回的是HTML（表单页面），说明有验证错误
                    if (typeof response === 'string' && response.includes('form')) {
                        // 解析错误信息
                        var $response = $(response);
                        var errors = {};

                        $response.find('.text-danger').each(function() {
                            var fieldName = $(this).closest('.form-group').find('input, select, textarea').attr('name');
                            if (fieldName) {
                                errors[fieldName] = $(this).text();
                            }
                        });

                        showValidationErrors(errors);
                    } else {
                        // 成功
                        toastr.success(isEditMode ? '分类更新成功！' : '分类创建成功！');
                        $('#categoryModal').modal('hide');
                        setTimeout(function() {
                            window.location.reload();
                        }, 1000);
                    }
                },
                error: function(xhr) {
                    if (xhr.status === 422) {
                        // 验证错误
                        var errors = xhr.responseJSON || {};
                        showValidationErrors(errors);
                    } else {
                        toastr.error('操作失败，请稍后重试！');
                    }
                },
                complete: function() {
                    // 恢复按钮状态
                    $saveBtn.prop('disabled', false).html(originalText);
                }
            });
        });

        // 显示验证错误
        function showValidationErrors(errors) {
            clearValidation();

            Object.keys(errors).forEach(function(field) {
                var $field = $('[name="' + field + '"]');
                var $group = $field.closest('.form-group');

                $field.addClass('is-invalid');
                $group.find('.invalid-feedback').text(errors[field]);
            });
        }

        // 清除验证状态
        function clearValidation() {
            $('.form-control').removeClass('is-invalid is-valid');
            $('.invalid-feedback').text('');
        }

        // 删除功能
        $('.delete-btn').click(function() {
            deleteId = $(this).data('id');
            $('#deleteModal').modal('show');
        });

        $('#confirmDelete').click(function() {
            if (deleteId) {
                var $btn = $(this);
                var originalText = $btn.html();
                $btn.prop('disabled', true).html('<i class="fas fa-spinner fa-spin mr-1"></i>删除中...');

                $.ajax({
                    url: '{{ url_for("ingredient_category.delete", id=0) }}'.replace('0', deleteId),
                    type: 'POST',
                    success: function(response) {
                        if (response.success) {
                            toastr.success(response.message);
                            setTimeout(function() {
                                window.location.reload();
                            }, 1000);
                        } else {
                            toastr.error(response.message);
                        }
                        $('#deleteModal').modal('hide');
                    },
                    error: function() {
                        toastr.error('删除失败，请稍后重试！');
                        $('#deleteModal').modal('hide');
                    },
                    complete: function() {
                        $btn.prop('disabled', false).html(originalText);
                    }
                });
            }
        });

        // 模态框关闭时清除验证状态
        $('#categoryModal').on('hidden.bs.modal', function() {
            clearValidation();
        });
    });
</script>
{% endblock %}
