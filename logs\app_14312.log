2025-06-22 18:51:58,112 INFO: 应用启动 - PID: 14312 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 18:51:59,479 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('balance_sheet_detail', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:56:25,623 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('purchase_order_list', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:59:19,615 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('ingredient_category', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:00:57,698 WARNING: [安全监控] 2025-06-22 19:00:57 - 可疑请求 | IP: *************:58120 | 路径: /, 指标: 无User-Agent [in C:\StudentsCMSSP\app\security_monitor.py:128]
2025-06-22 19:01:32,009 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-22 19:01:32,024 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-22 19:01:32,025 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-22 19:01:32,025 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-22 19:01:39,446 INFO: 查询菜谱：日期=2025-06-22, 星期=6(0=周一), day_of_week=7, 餐次=午餐, 区域ID=44 [in C:\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-22 19:01:39,464 INFO: 找到 1 个周菜单 [in C:\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-22 19:01:39,471 INFO: 匹配条件的食谱有 4 个 [in C:\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-22 19:01:39,492 INFO:   - 食谱: 红薯米饭（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 19:01:39,498 INFO:   - 食谱: 韭菜炒豆芽（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 19:01:39,514 INFO:   - 食谱: 蒸蛋羹（海淀区中关村第一小学版） [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 19:01:39,517 INFO:   - 食谱: 熟鸡蛋 [in C:\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-22 19:01:39,522 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=6, 多余=0 [in C:\StudentsCMSSP\app\routes\food_trace.py:531]
2025-06-22 19:06:53,742 WARNING: [安全监控] 2025-06-22 19:06:53 - 可疑请求 | IP: ************** | 路径: /google.com:443, 指标: 可疑HTTP方法 [in C:\StudentsCMSSP\app\security_monitor.py:128]
