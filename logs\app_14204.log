2025-06-22 19:10:57,115 INFO: 应用启动 - PID: 14204 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 19:11:41,361 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('weekly_menu_plan', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:11:56,733 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('financial_overview', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:11:58,361 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('financial_overview', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:12:50,200 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('training_records', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:13:31,614 INFO: 更新了日期 2025-06-16 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,624 INFO: 更新了日期 2025-06-17 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,644 INFO: 更新了日期 2025-06-19 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,659 INFO: 更新了日期 2025-06-20 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,661 INFO: 更新了日期 2025-06-21 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,673 INFO: 更新了日期 2025-06-22 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:31,673 INFO: 同步周菜单到工作日志成功: 更新=6, 创建=0, 跳过=1 [in C:\StudentsCMSSP\app\routes\menu_sync.py:64]
2025-06-22 19:13:33,943 INFO: 更新了日期 2025-06-09 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:33,960 INFO: 更新了日期 2025-06-13 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:33,980 INFO: 更新了日期 2025-06-14 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:33,991 INFO: 更新了日期 2025-06-15 的工作日志菜单 [in C:\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-22 19:13:33,992 INFO: 同步周菜单到工作日志成功: 更新=4, 创建=0, 跳过=3 [in C:\StudentsCMSSP\app\routes\menu_sync.py:64]
2025-06-22 19:16:54,917 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('dining_companions', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:17:09,178 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('dining_companions', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:17:09,405 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('ingredient_category', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:17:28,097 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('weekly_menu_plan', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:19:33,383 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('financial_overview', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 19:21:13,568 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('balance_sheet_detail', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
