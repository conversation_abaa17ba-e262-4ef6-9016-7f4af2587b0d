import type { OperationParameter, OperationSpec } from "./interfaces.js";
/**
 * Gets the list of status codes for streaming responses.
 * @internal
 */
export declare function getStreamingResponseStatusCodes(operationSpec: OperationSpec): Set<number>;
/**
 * Get the path to this parameter's value as a dotted string (a.b.c).
 * @param parameter - The parameter to get the path string for.
 * @returns The path to this parameter's value as a dotted string.
 * @internal
 */
export declare function getPathStringFromParameter(parameter: OperationParameter): string;
//# sourceMappingURL=interfaceHelpers.d.ts.map