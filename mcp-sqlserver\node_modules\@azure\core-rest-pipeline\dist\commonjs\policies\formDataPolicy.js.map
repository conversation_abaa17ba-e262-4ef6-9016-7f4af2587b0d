{"version": 3, "file": "formDataPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/formDataPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAiBlC,wCAEC;AAfD,0EAGqD;AAErD;;GAEG;AACU,QAAA,kBAAkB,GAAG,6BAAqB,CAAC;AAExD;;GAEG;AACH,SAAgB,cAAc;IAC5B,OAAO,IAAA,yBAAiB,GAAE,CAAC;AAC7B,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  formDataPolicyName as tspFormDataPolicyName,\n  formDataPolicy as tspFormDataPolicy,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the formDataPolicy.\n */\nexport const formDataPolicyName = tspFormDataPolicyName;\n\n/**\n * A policy that encodes FormData on the request into the body.\n */\nexport function formDataPolicy(): PipelinePolicy {\n  return tspFormDataPolicy();\n}\n"]}