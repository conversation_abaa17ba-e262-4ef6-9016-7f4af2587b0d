#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灰色表头样式查找工具
从颜色处理分析报告中找出所有将表头背景色设置为灰色的样式
"""

import re
from datetime import datetime
from collections import defaultdict

def extract_gray_header_styles():
    """从颜色分析报告中提取灰色表头样式"""
    
    try:
        with open("表头样式类颜色处理分析报告.md", 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取报告文件失败: {e}")
        return None
    
    results = {
        'gray_background_styles': [],
        'gray_text_styles': [],
        'mixed_gray_styles': [],
        'summary': {
            'total_gray_styles': 0,
            'files_with_gray': set(),
            'gray_color_values': set()
        }
    }
    
    # 定义灰色相关的颜色值模式
    gray_patterns = [
        # 十六进制灰色
        r'#[0-9a-fA-F]{3,6}',
        # RGB灰色
        r'rgb\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*\)',
        # RGBA灰色
        r'rgba\s*\(\s*\d+\s*,\s*\d+\s*,\s*\d+\s*,\s*[\d.]+\s*\)',
        # 灰色名称
        r'\b(gray|grey|lightgray|lightgrey|darkgray|darkgrey)\b',
        # CSS变量中的灰色
        r'var\s*\(\s*--[^)]*gray[^)]*\)',
        r'var\s*\(\s*--[^)]*grey[^)]*\)',
        # 企业级变量中的灰色
        r'var\s*\(\s*--enterprise-gray[^)]*\)',
        r'var\s*\(\s*--theme-gray[^)]*\)'
    ]
    
    def is_gray_color(color_value):
        """判断颜色值是否为灰色"""
        color_lower = color_value.lower()
        
        # 检查灰色关键词
        gray_keywords = ['gray', 'grey', 'lightgray', 'lightgrey', 'darkgray', 'darkgrey']
        if any(keyword in color_lower for keyword in gray_keywords):
            return True
        
        # 检查十六进制灰色 (RGB值相近的颜色)
        hex_match = re.match(r'#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})', color_value)
        if hex_match:
            hex_val = hex_match.group(1)
            if len(hex_val) == 3:
                r, g, b = int(hex_val[0], 16), int(hex_val[1], 16), int(hex_val[2], 16)
            else:
                r, g, b = int(hex_val[0:2], 16), int(hex_val[2:4], 16), int(hex_val[4:6], 16)
            
            # 判断是否为灰色 (RGB值差异小于30)
            if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
                return True
        
        # 检查RGB/RGBA灰色
        rgb_match = re.match(r'rgba?\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)', color_value)
        if rgb_match:
            r, g, b = int(rgb_match.group(1)), int(rgb_match.group(2)), int(rgb_match.group(3))
            if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
                return True
        
        # 检查CSS变量中的灰色
        if 'gray' in color_lower or 'grey' in color_lower:
            return True
        
        return False
    
    def extract_color_value(color_definition):
        """从颜色定义中提取颜色值"""
        # 匹配 `property: value` 格式
        match = re.search(r'`[^:]+:\s*([^`]+)`', color_definition)
        if match:
            return match.group(1).strip()
        return None
    
    # 解析报告内容
    lines = content.split('\n')
    current_style = None
    current_file = None
    current_selector = None
    current_type = None
    in_color_definition = False
    
    for i, line in enumerate(lines):
        line = line.strip()
        
        # 检测样式类名
        style_match = re.match(r'### `([^`]+)`', line)
        if style_match:
            current_style = style_match.group(1)
            continue
        
        # 检测文件名
        file_match = re.match(r'\*\*文件\*\*: `([^`]+)`', line)
        if file_match:
            current_file = file_match.group(1)
            continue
        
        # 检测选择器
        selector_match = re.match(r'\*\*选择器\*\*: `([^`]+)`', line)
        if selector_match:
            current_selector = selector_match.group(1)
            continue
        
        # 检测定义类型
        type_match = re.match(r'\*\*定义类型\*\*: (.+)', line)
        if type_match:
            current_type = type_match.group(1)
            continue
        
        # 检测颜色定义开始
        if line == '**具体颜色定义**:':
            in_color_definition = True
            continue
        
        # 检测颜色定义行
        if in_color_definition and line.startswith('- `'):
            color_value = extract_color_value(line)
            if color_value and is_gray_color(color_value):
                # 判断是背景色还是文字颜色
                if 'background' in line.lower():
                    category = 'background'
                elif line.lower().startswith('- `color:'):
                    category = 'text'
                else:
                    category = 'mixed'
                
                style_info = {
                    'style_name': current_style,
                    'file': current_file,
                    'selector': current_selector,
                    'type': current_type,
                    'color_definition': line,
                    'color_value': color_value,
                    'category': category
                }
                
                if category == 'background':
                    results['gray_background_styles'].append(style_info)
                elif category == 'text':
                    results['gray_text_styles'].append(style_info)
                else:
                    results['mixed_gray_styles'].append(style_info)
                
                results['summary']['total_gray_styles'] += 1
                results['summary']['files_with_gray'].add(current_file)
                results['summary']['gray_color_values'].add(color_value)
        
        # 检测段落结束
        if line == '---' or line.startswith('##'):
            in_color_definition = False
            current_style = None
            current_file = None
            current_selector = None
            current_type = None
    
    return results

def generate_gray_header_report(results):
    """生成灰色表头样式报告"""
    
    if not results:
        return "无法生成报告：数据为空"
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 灰色表头样式分析报告",
        "",
        f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 灰色样式统计概览",
        "",
        f"- **灰色样式总数**: {results['summary']['total_gray_styles']}",
        f"- **涉及文件数**: {len(results['summary']['files_with_gray'])}",
        f"- **灰色背景样式**: {len(results['gray_background_styles'])}",
        f"- **灰色文字样式**: {len(results['gray_text_styles'])}",
        f"- **混合灰色样式**: {len(results['mixed_gray_styles'])}",
        f"- **唯一灰色值数量**: {len(results['summary']['gray_color_values'])}",
        "",
    ])
    
    # 灰色背景样式 (重点关注)
    if results['gray_background_styles']:
        report_lines.extend([
            "## 🎨 灰色背景表头样式 (需要重点关注)",
            "",
            "这些样式将表头背景色设置为灰色，建议替换为系统主题变量：",
            "",
        ])
        
        # 按文件分组
        file_groups = defaultdict(list)
        for style in results['gray_background_styles']:
            file_groups[style['file']].append(style)
        
        for file_path in sorted(file_groups.keys()):
            styles = file_groups[file_path]
            report_lines.append(f"### 📁 `{file_path}`")
            report_lines.append("")
            
            for style in styles:
                report_lines.append(f"#### 样式类: `{style['style_name']}`")
                report_lines.append(f"- **选择器**: `{style['selector']}`")
                report_lines.append(f"- **定义类型**: {style['type']}")
                report_lines.append(f"- **颜色定义**: {style['color_definition']}")
                report_lines.append(f"- **建议替换为**: `var(--theme-primary, #007bff)` (主题色)")
                report_lines.append("")
            
            report_lines.append("---")
            report_lines.append("")
    
    # 灰色文字样式
    if results['gray_text_styles']:
        report_lines.extend([
            "## 📝 灰色文字表头样式",
            "",
            "这些样式将表头文字颜色设置为灰色：",
            "",
        ])
        
        for style in results['gray_text_styles']:
            report_lines.append(f"### `{style['style_name']}` - `{style['file']}`")
            report_lines.append(f"- **选择器**: `{style['selector']}`")
            report_lines.append(f"- **颜色定义**: {style['color_definition']}")
            report_lines.append("")
    
    # 混合灰色样式
    if results['mixed_gray_styles']:
        report_lines.extend([
            "## 🎭 混合灰色样式",
            "",
            "这些样式包含其他类型的灰色定义：",
            "",
        ])
        
        for style in results['mixed_gray_styles']:
            report_lines.append(f"### `{style['style_name']}` - `{style['file']}`")
            report_lines.append(f"- **选择器**: `{style['selector']}`")
            report_lines.append(f"- **颜色定义**: {style['color_definition']}")
            report_lines.append("")
    
    # 发现的灰色值汇总
    if results['summary']['gray_color_values']:
        report_lines.extend([
            "## 🎯 发现的灰色值汇总",
            "",
            "项目中使用的所有灰色值：",
            "",
        ])
        
        for color_value in sorted(results['summary']['gray_color_values']):
            report_lines.append(f"- `{color_value}`")
        
        report_lines.append("")
    
    # 优化建议
    report_lines.extend([
        "## 💡 优化建议",
        "",
        "### 🎯 表头背景色统一化",
        "",
        "1. **替换灰色表头背景**: 将所有灰色表头背景替换为系统主题色",
        "   ```css",
        "   /* 替换前 */",
        "   background-color: #f8f9fa;",
        "   background-color: gray;",
        "   ",
        "   /* 替换后 */",
        "   background-color: var(--theme-primary, #007bff);",
        "   ```",
        "",
        "2. **统一表头文字颜色**: 使用白色文字配合主题背景色",
        "   ```css",
        "   color: white !important;",
        "   ```",
        "",
        "3. **移除灰色主题**: 确保所有表头使用一致的主题色系",
        "",
        "### 🔧 具体修改步骤",
        "",
        "1. **批量替换**: 使用查找替换功能将灰色值替换为主题变量",
        "2. **测试验证**: 确保替换后的样式在所有页面正常显示",
        "3. **主题一致性**: 确保所有模块的表头样式保持一致",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始查找灰色表头样式...")
    
    # 提取灰色表头样式
    results = extract_gray_header_styles()
    
    if not results:
        print("❌ 提取失败")
        return
    
    print(f"📊 分析完成:")
    print(f"   - 灰色样式总数: {results['summary']['total_gray_styles']}")
    print(f"   - 灰色背景样式: {len(results['gray_background_styles'])}")
    print(f"   - 灰色文字样式: {len(results['gray_text_styles'])}")
    print(f"   - 涉及文件数: {len(results['summary']['files_with_gray'])}")
    
    # 生成报告
    print("\n📝 生成灰色表头分析报告...")
    report_content = generate_gray_header_report(results)
    
    # 保存报告
    report_file = "灰色表头样式分析报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 报告已生成: {report_file}")
    
    # 显示重点发现
    if results['gray_background_styles']:
        print(f"\n🎯 发现 {len(results['gray_background_styles'])} 个灰色背景表头样式:")
        for style in results['gray_background_styles'][:5]:  # 只显示前5个
            print(f"   - {style['style_name']} ({style['file']})")
        if len(results['gray_background_styles']) > 5:
            print(f"   ... 还有 {len(results['gray_background_styles']) - 5} 个")

if __name__ == "__main__":
    main()
