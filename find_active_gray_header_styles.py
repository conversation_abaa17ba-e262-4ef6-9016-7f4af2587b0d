#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
活跃灰色表头样式查找工具
找出当前项目中实际使用的灰色表头样式
"""

import os
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

def extract_table_classes_from_html():
    """从HTML模板中提取所有表格相关的CSS类"""
    
    templates_dir = Path("app/templates")
    table_classes = defaultdict(list)  # {class_name: [file_paths]}
    
    # 表格相关标签的模式
    patterns = {
        "table_tags": re.compile(r'<table[^>]*class="([^"]*)"[^>]*>', re.IGNORECASE),
        "thead_tags": re.compile(r'<thead[^>]*class="([^"]*)"[^>]*>', re.IGNORECASE),
        "th_tags": re.compile(r'<th[^>]*class="([^"]*)"[^>]*>', re.IGNORECASE),
        "tr_tags": re.compile(r'<tr[^>]*class="([^"]*)"[^>]*>', re.IGNORECASE),
        "td_tags": re.compile(r'<td[^>]*class="([^"]*)"[^>]*>', re.IGNORECASE)
    }
    
    def process_html_file(file_path):
        """处理单个HTML文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取HTML文件失败 {file_path}: {e}")
            return
        
        relative_path = str(file_path.relative_to(templates_dir))
        
        # 提取所有表格相关的class属性
        for tag_type, pattern in patterns.items():
            matches = pattern.findall(content)
            for class_attr in matches:
                if class_attr.strip():
                    # 分割多个class
                    classes = class_attr.split()
                    for cls in classes:
                        if cls.strip():
                            table_classes[cls.strip()].append({
                                'file': relative_path,
                                'tag_type': tag_type,
                                'full_class': class_attr
                            })
    
    # 扫描所有HTML文件
    if templates_dir.exists():
        for html_file in templates_dir.rglob("*.html"):
            process_html_file(html_file)
    
    return table_classes

def load_gray_styles_from_report():
    """从灰色表头分析报告中加载灰色样式信息"""
    
    try:
        with open("灰色表头样式分析报告.md", 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        print(f"读取灰色样式报告失败: {e}")
        return {}
    
    gray_styles = {}
    
    # 解析报告中的样式信息
    lines = content.split('\n')
    current_style = None
    current_file = None
    current_selector = None
    current_definition = None
    
    for line in lines:
        line = line.strip()
        
        # 检测样式类名
        style_match = re.match(r'#### 样式类: `([^`]+)`', line)
        if style_match:
            current_style = style_match.group(1)
            continue
        
        # 检测选择器
        selector_match = re.match(r'- \*\*选择器\*\*: `([^`]+)`', line)
        if selector_match:
            current_selector = selector_match.group(1)
            continue
        
        # 检测颜色定义
        definition_match = re.match(r'- \*\*颜色定义\*\*: (.+)', line)
        if definition_match:
            current_definition = definition_match.group(1)
            
            if current_style and current_selector and current_definition:
                if current_style not in gray_styles:
                    gray_styles[current_style] = []
                
                gray_styles[current_style].append({
                    'selector': current_selector,
                    'definition': current_definition
                })
    
    return gray_styles

def find_css_definitions(class_names):
    """在CSS文件中查找指定类名的定义"""
    
    css_definitions = defaultdict(list)
    
    # CSS文件目录
    css_dirs = [
        Path("app/static/css"),
        Path("app/static/bootstrap/css"),
        Path("app/static/financial/css"),
        Path("app/static/vendor")
    ]
    
    def process_css_file(file_path):
        """处理CSS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取CSS文件失败 {file_path}: {e}")
            return
        
        # 移除注释
        content = re.sub(r'/\*.*?\*/', '', content, flags=re.DOTALL)
        
        # 查找CSS规则
        for class_name in class_names:
            # 多种选择器模式
            patterns = [
                rf'\.{re.escape(class_name)}\s*{{[^}}]*background[^}}]*}}',
                rf'\.{re.escape(class_name)}[^{{]*{{[^}}]*background[^}}]*}}',
                rf'[^{{]*\.{re.escape(class_name)}[^{{]*{{[^}}]*background[^}}]*}}',
                rf'\.table[^{{]*\.{re.escape(class_name)}[^{{]*{{[^}}]*background[^}}]*}}'
            ]
            
            for pattern in patterns:
                matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
                for match in matches:
                    # 检查是否包含灰色
                    if is_gray_background(match):
                        css_definitions[class_name].append({
                            'file': str(file_path),
                            'rule': match.strip(),
                            'type': 'css'
                        })
    
    # 扫描CSS文件
    for css_dir in css_dirs:
        if css_dir.exists():
            for css_file in css_dir.rglob("*.css"):
                process_css_file(css_file)
    
    return css_definitions

def is_gray_background(css_rule):
    """判断CSS规则是否包含灰色背景"""
    
    # 提取background相关属性
    bg_pattern = r'background[^:]*:\s*([^;!]+)(!important)?'
    bg_matches = re.findall(bg_pattern, css_rule, re.IGNORECASE)
    
    for bg_value, _ in bg_matches:
        if is_gray_color(bg_value.strip()):
            return True
    
    return False

def is_gray_color(color_value):
    """判断颜色值是否为灰色"""
    color_lower = color_value.lower()
    
    # 检查灰色关键词
    gray_keywords = ['gray', 'grey', 'lightgray', 'lightgrey', 'darkgray', 'darkgrey']
    if any(keyword in color_lower for keyword in gray_keywords):
        return True
    
    # 检查十六进制灰色
    hex_match = re.match(r'#([0-9a-fA-F]{3}|[0-9a-fA-F]{6})', color_value)
    if hex_match:
        hex_val = hex_match.group(1)
        if len(hex_val) == 3:
            r, g, b = int(hex_val[0], 16), int(hex_val[1], 16), int(hex_val[2], 16)
        else:
            r, g, b = int(hex_val[0:2], 16), int(hex_val[2:4], 16), int(hex_val[4:6], 16)
        
        # 判断是否为灰色 (RGB值差异小于30)
        if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
            return True
    
    # 检查RGB/RGBA灰色
    rgb_match = re.match(r'rgba?\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)', color_value)
    if rgb_match:
        r, g, b = int(rgb_match.group(1)), int(rgb_match.group(2)), int(rgb_match.group(3))
        if abs(r - g) < 30 and abs(g - b) < 30 and abs(r - b) < 30:
            return True
    
    # 检查常见的灰色值
    common_grays = ['#f8f9fa', '#e9ecef', '#dee2e6', '#ced4da', '#adb5bd', '#6c757d', '#495057', '#343a40']
    if color_value in common_grays:
        return True
    
    return False

def analyze_active_gray_styles():
    """分析活跃的灰色表头样式"""
    
    print("🔍 步骤1: 提取HTML模板中的表格CSS类...")
    html_table_classes = extract_table_classes_from_html()
    print(f"   发现 {len(html_table_classes)} 个唯一的表格CSS类")
    
    print("🔍 步骤2: 查找这些类的CSS定义...")
    css_definitions = find_css_definitions(html_table_classes.keys())
    print(f"   发现 {len(css_definitions)} 个类有灰色背景定义")
    
    print("🔍 步骤3: 分析活跃的灰色样式...")
    
    active_gray_styles = {}
    
    for class_name, css_defs in css_definitions.items():
        if class_name in html_table_classes:
            html_usage = html_table_classes[class_name]
            
            active_gray_styles[class_name] = {
                'css_definitions': css_defs,
                'html_usage': html_usage,
                'usage_count': len(html_usage),
                'files_using': list(set([usage['file'] for usage in html_usage]))
            }
    
    return active_gray_styles

def generate_active_gray_report(active_styles):
    """生成活跃灰色样式报告"""
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 当前项目活跃灰色表头样式报告",
        "",
        f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 活跃灰色样式概览",
        "",
        f"- **活跃灰色样式类数量**: {len(active_styles)}",
        f"- **总使用次数**: {sum(style['usage_count'] for style in active_styles.values())}",
        "",
        "## 🎯 需要立即处理的活跃灰色表头样式",
        "",
        "以下样式类在项目中实际使用，且定义了灰色背景，需要优先处理：",
        "",
    ])
    
    # 按使用频率排序
    sorted_styles = sorted(active_styles.items(), key=lambda x: x[1]['usage_count'], reverse=True)
    
    for class_name, style_info in sorted_styles:
        report_lines.append(f"### 🔥 `{class_name}` (使用 {style_info['usage_count']} 次)")
        report_lines.append("")
        
        # CSS定义
        report_lines.append("**CSS定义**:")
        for css_def in style_info['css_definitions']:
            report_lines.append(f"- **文件**: `{css_def['file']}`")
            report_lines.append(f"  ```css")
            report_lines.append(f"  {css_def['rule']}")
            report_lines.append(f"  ```")
            report_lines.append("")
        
        # HTML使用情况
        report_lines.append("**HTML使用情况**:")
        file_usage = defaultdict(list)
        for usage in style_info['html_usage']:
            file_usage[usage['file']].append(usage['tag_type'])
        
        for file_path, tag_types in file_usage.items():
            unique_tags = list(set(tag_types))
            report_lines.append(f"- `{file_path}` - 标签类型: {', '.join(unique_tags)}")
        
        report_lines.append("")
        
        # 优化建议
        report_lines.append("**🔧 优化建议**:")
        if 'thead' in class_name.lower() or 'header' in class_name.lower():
            report_lines.append("- 表头样式，建议替换为: `var(--theme-primary, #007bff)`")
        elif 'striped' in class_name.lower():
            report_lines.append("- 条纹样式，建议替换为: `var(--theme-striped-bg, rgba(0,0,0,0.05))`")
        elif 'light' in class_name.lower():
            report_lines.append("- 浅色样式，建议替换为: `var(--theme-gray-100, #f8f9fa)`")
        else:
            report_lines.append("- 建议使用系统主题变量替换硬编码颜色值")
        
        report_lines.append("")
        report_lines.append("---")
        report_lines.append("")
    
    # 优化优先级
    report_lines.extend([
        "## 🚨 优化优先级建议",
        "",
        "### 🔥 高优先级 (立即处理)",
        "",
    ])
    
    high_priority = [style for style in sorted_styles if style[1]['usage_count'] >= 5]
    if high_priority:
        for class_name, style_info in high_priority:
            report_lines.append(f"- **`{class_name}`**: 使用 {style_info['usage_count']} 次，影响 {len(style_info['files_using'])} 个文件")
    else:
        report_lines.append("- 无高频使用的灰色样式")
    
    report_lines.append("")
    
    report_lines.extend([
        "### ⚡ 中优先级 (计划处理)",
        "",
    ])
    
    medium_priority = [style for style in sorted_styles if 2 <= style[1]['usage_count'] < 5]
    if medium_priority:
        for class_name, style_info in medium_priority:
            report_lines.append(f"- **`{class_name}`**: 使用 {style_info['usage_count']} 次")
    else:
        report_lines.append("- 无中频使用的灰色样式")
    
    report_lines.append("")
    
    report_lines.extend([
        "### 📝 低优先级 (后续处理)",
        "",
    ])
    
    low_priority = [style for style in sorted_styles if style[1]['usage_count'] < 2]
    if low_priority:
        for class_name, style_info in low_priority:
            report_lines.append(f"- **`{class_name}`**: 使用 {style_info['usage_count']} 次")
    else:
        report_lines.append("- 无低频使用的灰色样式")
    
    report_lines.append("")
    
    # 具体修改建议
    report_lines.extend([
        "## 💡 具体修改建议",
        "",
        "### 🎯 批量替换方案",
        "",
        "1. **表头背景色统一**:",
        "   ```css",
        "   /* 将所有表头灰色背景替换为主题色 */",
        "   background-color: var(--theme-primary, #007bff);",
        "   color: white;",
        "   ```",
        "",
        "2. **条纹背景色统一**:",
        "   ```css",
        "   /* 将条纹背景替换为主题变量 */",
        "   background-color: var(--theme-striped-bg, rgba(0,0,0,0.05));",
        "   ```",
        "",
        "3. **悬停效果统一**:",
        "   ```css",
        "   /* 将悬停背景替换为主题变量 */",
        "   background-color: var(--theme-hover-bg, rgba(0,123,255,0.1));",
        "   ```",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始分析当前项目活跃的灰色表头样式...")
    
    # 分析活跃的灰色样式
    active_styles = analyze_active_gray_styles()
    
    print(f"📊 分析完成:")
    print(f"   - 发现 {len(active_styles)} 个活跃的灰色样式类")
    
    if active_styles:
        # 显示前5个最常用的
        sorted_styles = sorted(active_styles.items(), key=lambda x: x[1]['usage_count'], reverse=True)
        print(f"\n🎯 使用最频繁的灰色样式:")
        for class_name, style_info in sorted_styles[:5]:
            print(f"   - {class_name}: {style_info['usage_count']} 次使用")
    
    # 生成报告
    print("\n📝 生成活跃灰色样式报告...")
    report_content = generate_active_gray_report(active_styles)
    
    # 保存报告
    report_file = "当前项目活跃灰色表头样式报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 报告已生成: {report_file}")

if __name__ == "__main__":
    main()
