{"version": 3, "file": "poller.js", "sourceRoot": "", "sources": ["../../../src/legacy/poller.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAclC;;;GAGG;AACH,MAAa,kBAAmB,SAAQ,KAAK;IAC3C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;QACjC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,kBAAkB,CAAC,SAAS,CAAC,CAAC;IAC5D,CAAC;CACF;AAND,gDAMC;AAED;;;GAGG;AACH,MAAa,oBAAqB,SAAQ,KAAK;IAC7C,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,sBAAsB,CAAC;QACnC,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,oBAAoB,CAAC,SAAS,CAAC,CAAC;IAC9D,CAAC;CACF;AAND,oDAMC;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GA4DG;AACH,gDAAgD;AAChD,MAAsB,MAAM;IAmB1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgEG;IACH,YAAY,SAAyC;QAjFrD,kFAAkF;QACxE,0BAAqB,GAAY,KAAK,CAAC;QACzC,YAAO,GAAY,IAAI,CAAC;QAMxB,0BAAqB,GAAmC,EAAE,CAAC;QA0EjE,IAAI,CAAC,SAAS,GAAG,SAAS,CAAC;QAC3B,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO,CACxB,CACE,OAAkC,EAClC,MAA0E,EAC1E,EAAE;YACF,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;YACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACvB,CAAC,CACF,CAAC;QACF,mFAAmF;QACnF,sFAAsF;QACtF,mFAAmF;QACnF,IAAI,CAAC,OAAO,CAAC,KAAK,CAAC,GAAG,EAAE;YACtB,yBAAyB;QAC3B,CAAC,CAAC,CAAC;IACL,CAAC;IAyBD;;;OAGG;IACK,KAAK,CAAC,YAAY,CAAC,cAAiD,EAAE;QAC5E,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,OAAO,GAAG,KAAK,CAAC;QACvB,CAAC;QACD,OAAO,CAAC,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC,CAAC;YAC7B,MAAM,IAAI,CAAC,KAAK,EAAE,CAAC;QACrB,CAAC;IACH,CAAC;IAED;;;;;;;OAOG;IACK,KAAK,CAAC,QAAQ,CAAC,UAA6C,EAAE;QACpE,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE,EAAE,CAAC;YACnB,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;gBAC3C,WAAW,EAAE,OAAO,CAAC,WAAW;gBAChC,YAAY,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,CAAC;aAC3C,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,mBAAmB,EAAE,CAAC;IAC7B,CAAC;IAED;;;;;;;OAOG;IACK,YAAY,CAAC,KAAa;QAChC,KAAK,MAAM,QAAQ,IAAI,IAAI,CAAC,qBAAqB,EAAE,CAAC;YAClD,QAAQ,CAAC,KAAK,CAAC,CAAC;QAClB,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,UAAU,CAAC,UAA6C,EAAE;QACtE,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC;IACxD,CAAC;IAED;;;;;;;OAOG;IACI,IAAI,CAAC,UAA6C,EAAE;QACzD,IAAI,CAAC,IAAI,CAAC,eAAe,EAAE,CAAC;YAC1B,IAAI,CAAC,eAAe,GAAG,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YAC9C,MAAM,oBAAoB,GAAG,GAAS,EAAE;gBACtC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;YACnC,CAAC,CAAC;YACF,IAAI,CAAC,eAAe,CAAC,IAAI,CAAC,oBAAoB,EAAE,oBAAoB,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QAC3F,CAAC;QACD,OAAO,IAAI,CAAC,eAAe,CAAC;IAC9B,CAAC;IAEO,mBAAmB;QACzB,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,EAAE,CAAC;YAC/B,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAChC,IAAI,CAAC,MAAO,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;gBACzC,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,KAAK,CAAC;YACnC,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC;YACrC,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE,CAAC;gBAChC,MAAM,KAAK,GAAG,IAAI,oBAAoB,CAAC,wBAAwB,CAAC,CAAC;gBACjE,IAAI,CAAC,MAAO,CAAC,KAAK,CAAC,CAAC;gBACpB,MAAM,KAAK,CAAC;YACd,CAAC;QACH,CAAC;QACD,IAAI,IAAI,CAAC,MAAM,EAAE,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YAClC,uEAAuE;YACvE,uEAAuE;YACvE,oEAAoE;YACpE,uEAAuE;YACvE,cAAc;YACd,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,SAAS,EAAa,CAAC,CAAC;QAC5C,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,aAAa,CACxB,cAAiD,EAAE;QAEnD,IAAI,IAAI,CAAC,OAAO,EAAE,CAAC;YACjB,IAAI,CAAC,YAAY,CAAC,WAAW,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;QACpD,CAAC;QACD,8DAA8D;QAC9D,0EAA0E;QAC1E,IAAI,CAAC,mBAAmB,EAAE,CAAC;QAC3B,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;;;OAKG;IACI,UAAU,CAAC,QAAiC;QACjD,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;QAC1C,OAAO,GAAS,EAAE;YAChB,IAAI,CAAC,qBAAqB,GAAG,IAAI,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC,KAAK,QAAQ,CAAC,CAAC;QACxF,CAAC,CAAC;IACJ,CAAC;IAED;;OAEG;IACI,MAAM;QACX,MAAM,KAAK,GAAgC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAChE,OAAO,OAAO,CAAC,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,KAAK,CAAC,CAAC;IACxE,CAAC;IAED;;OAEG;IACI,WAAW;QAChB,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE,CAAC;YAClB,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC;YACpB,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,IAAI,kBAAkB,CAAC,gCAAgC,CAAC,CAAC,CAAC;YACxE,CAAC;QACH,CAAC;IACH,CAAC;IAED;;OAEG;IACI,SAAS;QACd,OAAO,IAAI,CAAC,OAAO,CAAC;IACtB,CAAC;IAED;;;;;;;;OAQG;IACI,eAAe,CAAC,UAA6C,EAAE;QACpE,IAAI,CAAC,IAAI,CAAC,aAAa,EAAE,CAAC;YACxB,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC,UAAU,CAAC,OAAO,CAAC,CAAC;QAChD,CAAC;aAAM,IAAI,OAAO,CAAC,WAAW,EAAE,CAAC;YAC/B,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QACD,OAAO,IAAI,CAAC,aAAa,CAAC;IAC5B,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OA+CG;IACI,iBAAiB;QACtB,OAAO,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;IAC9B,CAAC;IAED;;;;;OAKG;IACI,SAAS;QACd,MAAM,KAAK,GAAgC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;QAChE,OAAO,KAAK,CAAC,MAAM,CAAC;IACtB,CAAC;IAED;;;OAGG;IACI,QAAQ;QACb,OAAO,IAAI,CAAC,SAAS,CAAC,QAAQ,EAAE,CAAC;IACnC,CAAC;CACF;AA/WD,wBA+WC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT license.\n\nimport { PollOperation, PollOperationState } from \"./pollOperation.js\";\nimport { AbortSignalLike } from \"@azure/abort-controller\";\nimport { CancelOnProgress } from \"../poller/models.js\";\nimport { PollerLike } from \"./models.js\";\n\n/**\n * PollProgressCallback<TState> is the type of the callback functions sent to onProgress.\n * These functions will receive a TState that is defined by your implementation of\n * the Poller class.\n */\nexport type PollProgressCallback<TState> = (state: TState) => void;\n\n/**\n * When a poller is manually stopped through the `stopPolling` method,\n * the poller will be rejected with an instance of the PollerStoppedError.\n */\nexport class PollerStoppedError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"PollerStoppedError\";\n    Object.setPrototypeOf(this, PollerStoppedError.prototype);\n  }\n}\n\n/**\n * When the operation is cancelled, the poller will be rejected with an instance\n * of the PollerCancelledError.\n */\nexport class PollerCancelledError extends Error {\n  constructor(message: string) {\n    super(message);\n    this.name = \"PollerCancelledError\";\n    Object.setPrototypeOf(this, PollerCancelledError.prototype);\n  }\n}\n\n/**\n * A class that represents the definition of a program that polls through consecutive requests\n * until it reaches a state of completion.\n *\n * A poller can be executed manually, by polling request by request by calling to the `poll()` method repeatedly, until its operation is completed.\n * It also provides a way to wait until the operation completes, by calling `pollUntilDone()` and waiting until the operation finishes.\n * Pollers can also request the cancellation of the ongoing process to whom is providing the underlying long running operation.\n *\n * ```ts\n * const poller = new MyPoller();\n *\n * // Polling just once:\n * await poller.poll();\n *\n * // We can try to cancel the request here, by calling:\n * //\n * //     await poller.cancelOperation();\n * //\n *\n * // Getting the final result:\n * const result = await poller.pollUntilDone();\n * ```\n *\n * The Poller is defined by two types, a type representing the state of the poller, which\n * must include a basic set of properties from `PollOperationState<TResult>`,\n * and a return type defined by `TResult`, which can be anything.\n *\n * The Poller class implements the `PollerLike` interface, which allows poller implementations to avoid having\n * to export the Poller's class directly, and instead only export the already instantiated poller with the PollerLike type.\n *\n * ```ts\n * class Client {\n *   public async makePoller: PollerLike<MyOperationState, MyResult> {\n *     const poller = new MyPoller({});\n *     // It might be preferred to return the poller after the first request is made,\n *     // so that some information can be obtained right away.\n *     await poller.poll();\n *     return poller;\n *   }\n * }\n *\n * const poller: PollerLike<MyOperationState, MyResult> = myClient.makePoller();\n * ```\n *\n * A poller can be created through its constructor, then it can be polled until it's completed.\n * At any point in time, the state of the poller can be obtained without delay through the getOperationState method.\n * At any point in time, the intermediate forms of the result type can be requested without delay.\n * Once the underlying operation is marked as completed, the poller will stop and the final value will be returned.\n *\n * ```ts\n * const poller = myClient.makePoller();\n * const state: MyOperationState = poller.getOperationState();\n *\n * // The intermediate result can be obtained at any time.\n * const result: MyResult | undefined = poller.getResult();\n *\n * // The final result can only be obtained after the poller finishes.\n * const result: MyResult = await poller.pollUntilDone();\n * ```\n *\n */\n// eslint-disable-next-line no-use-before-define\nexport abstract class Poller<TState extends PollOperationState<TResult>, TResult>\n  implements PollerLike<TState, TResult>\n{\n  /** controls whether to throw an error if the operation failed or was canceled. */\n  protected resolveOnUnsuccessful: boolean = false;\n  private stopped: boolean = true;\n  private resolve?: (value: TResult) => void;\n  private reject?: (error: PollerStoppedError | PollerCancelledError | Error) => void;\n  private pollOncePromise?: Promise<void>;\n  private cancelPromise?: Promise<void>;\n  private promise: Promise<TResult>;\n  private pollProgressCallbacks: PollProgressCallback<TState>[] = [];\n\n  /**\n   * The poller's operation is available in full to any of the methods of the Poller class\n   * and any class extending the Poller class.\n   */\n  protected operation: PollOperation<TState, TResult>;\n\n  /**\n   * A poller needs to be initialized by passing in at least the basic properties of the `PollOperation<TState, TResult>`.\n   *\n   * When writing an implementation of a Poller, this implementation needs to deal with the initialization\n   * of any custom state beyond the basic definition of the poller. The basic poller assumes that the poller's\n   * operation has already been defined, at least its basic properties. The code below shows how to approach\n   * the definition of the constructor of a new custom poller.\n   *\n   * ```ts\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   constructor({\n   *     // Anything you might need outside of the basics\n   *   }) {\n   *     let state: MyOperationState = {\n   *       privateProperty: private,\n   *       publicProperty: public,\n   *     };\n   *\n   *     const operation = {\n   *       state,\n   *       update,\n   *       cancel,\n   *       toString\n   *     }\n   *\n   *     // Sending the operation to the parent's constructor.\n   *     super(operation);\n   *\n   *     // You can assign more local properties here.\n   *   }\n   * }\n   * ```\n   *\n   * Inside of this constructor, a new promise is created. This will be used to\n   * tell the user when the poller finishes (see `pollUntilDone()`). The promise's\n   * resolve and reject methods are also used internally to control when to resolve\n   * or reject anyone waiting for the poller to finish.\n   *\n   * The constructor of a custom implementation of a poller is where any serialized version of\n   * a previous poller's operation should be deserialized into the operation sent to the\n   * base constructor. For example:\n   *\n   * ```ts\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   constructor(\n   *     baseOperation: string | undefined\n   *   ) {\n   *     let state: MyOperationState = {};\n   *     if (baseOperation) {\n   *       state = {\n   *         ...JSON.parse(baseOperation).state,\n   *         ...state\n   *       };\n   *     }\n   *     const operation = {\n   *       state,\n   *       // ...\n   *     }\n   *     super(operation);\n   *   }\n   * }\n   * ```\n   *\n   * @param operation - Must contain the basic properties of `PollOperation<State, TResult>`.\n   */\n  constructor(operation: PollOperation<TState, TResult>) {\n    this.operation = operation;\n    this.promise = new Promise<TResult>(\n      (\n        resolve: (result: TResult) => void,\n        reject: (error: PollerStoppedError | PollerCancelledError | Error) => void,\n      ) => {\n        this.resolve = resolve;\n        this.reject = reject;\n      },\n    );\n    // This prevents the UnhandledPromiseRejectionWarning in node.js from being thrown.\n    // The above warning would get thrown if `poller.poll` is called, it returns an error,\n    // and pullUntilDone did not have a .catch or await try/catch on it's return value.\n    this.promise.catch(() => {\n      /* intentionally blank */\n    });\n  }\n\n  /**\n   * Defines how much to wait between each poll request.\n   * This has to be implemented by your custom poller.\n   *\n   * \\@azure/core-util has a simple implementation of a delay function that waits as many milliseconds as specified.\n   * This can be used as follows:\n   *\n   * ```ts\n   * import { delay } from \"@azure/core-util\";\n   *\n   * export class MyPoller extends Poller<MyOperationState, string> {\n   *   // The other necessary definitions.\n   *\n   *   async delay(): Promise<void> {\n   *     const milliseconds = 1000;\n   *     return delay(milliseconds);\n   *   }\n   * }\n   * ```\n   *\n   */\n  protected abstract delay(): Promise<void>;\n\n  /**\n   * Starts a loop that will break only if the poller is done\n   * or if the poller is stopped.\n   */\n  private async startPolling(pollOptions: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (this.stopped) {\n      this.stopped = false;\n    }\n    while (!this.isStopped() && !this.isDone()) {\n      await this.poll(pollOptions);\n      await this.delay();\n    }\n  }\n\n  /**\n   * pollOnce does one polling, by calling to the update method of the underlying\n   * poll operation to make any relevant change effective.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  private async pollOnce(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (!this.isDone()) {\n      this.operation = await this.operation.update({\n        abortSignal: options.abortSignal,\n        fireProgress: this.fireProgress.bind(this),\n      });\n    }\n    this.processUpdatedState();\n  }\n\n  /**\n   * fireProgress calls the functions passed in via onProgress the method of the poller.\n   *\n   * It loops over all of the callbacks received from onProgress, and executes them, sending them\n   * the current operation state.\n   *\n   * @param state - The current operation state.\n   */\n  private fireProgress(state: TState): void {\n    for (const callback of this.pollProgressCallbacks) {\n      callback(state);\n    }\n  }\n\n  /**\n   * Invokes the underlying operation's cancel method.\n   */\n  private async cancelOnce(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    this.operation = await this.operation.cancel(options);\n  }\n\n  /**\n   * Returns a promise that will resolve once a single polling request finishes.\n   * It does this by calling the update method of the Poller's operation.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  public poll(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (!this.pollOncePromise) {\n      this.pollOncePromise = this.pollOnce(options);\n      const clearPollOncePromise = (): void => {\n        this.pollOncePromise = undefined;\n      };\n      this.pollOncePromise.then(clearPollOncePromise, clearPollOncePromise).catch(this.reject);\n    }\n    return this.pollOncePromise;\n  }\n\n  private processUpdatedState(): void {\n    if (this.operation.state.error) {\n      this.stopped = true;\n      if (!this.resolveOnUnsuccessful) {\n        this.reject!(this.operation.state.error);\n        throw this.operation.state.error;\n      }\n    }\n    if (this.operation.state.isCancelled) {\n      this.stopped = true;\n      if (!this.resolveOnUnsuccessful) {\n        const error = new PollerCancelledError(\"Operation was canceled\");\n        this.reject!(error);\n        throw error;\n      }\n    }\n    if (this.isDone() && this.resolve) {\n      // If the poller has finished polling, this means we now have a result.\n      // However, it can be the case that TResult is instantiated to void, so\n      // we are not expecting a result anyway. To assert that we might not\n      // have a result eventually after finishing polling, we cast the result\n      // to TResult.\n      this.resolve(this.getResult() as TResult);\n    }\n  }\n\n  /**\n   * Returns a promise that will resolve once the underlying operation is completed.\n   */\n  public async pollUntilDone(\n    pollOptions: { abortSignal?: AbortSignalLike } = {},\n  ): Promise<TResult> {\n    if (this.stopped) {\n      this.startPolling(pollOptions).catch(this.reject);\n    }\n    // This is needed because the state could have been updated by\n    // `cancelOperation`, e.g. the operation is canceled or an error occurred.\n    this.processUpdatedState();\n    return this.promise;\n  }\n\n  /**\n   * Invokes the provided callback after each polling is completed,\n   * sending the current state of the poller's operation.\n   *\n   * It returns a method that can be used to stop receiving updates on the given callback function.\n   */\n  public onProgress(callback: (state: TState) => void): CancelOnProgress {\n    this.pollProgressCallbacks.push(callback);\n    return (): void => {\n      this.pollProgressCallbacks = this.pollProgressCallbacks.filter((c) => c !== callback);\n    };\n  }\n\n  /**\n   * Returns true if the poller has finished polling.\n   */\n  public isDone(): boolean {\n    const state: PollOperationState<TResult> = this.operation.state;\n    return Boolean(state.isCompleted || state.isCancelled || state.error);\n  }\n\n  /**\n   * Stops the poller from continuing to poll.\n   */\n  public stopPolling(): void {\n    if (!this.stopped) {\n      this.stopped = true;\n      if (this.reject) {\n        this.reject(new PollerStoppedError(\"This poller is already stopped\"));\n      }\n    }\n  }\n\n  /**\n   * Returns true if the poller is stopped.\n   */\n  public isStopped(): boolean {\n    return this.stopped;\n  }\n\n  /**\n   * Attempts to cancel the underlying operation.\n   *\n   * It only optionally receives an object with an abortSignal property, from \\@azure/abort-controller's AbortSignalLike.\n   *\n   * If it's called again before it finishes, it will throw an error.\n   *\n   * @param options - Optional properties passed to the operation's update method.\n   */\n  public cancelOperation(options: { abortSignal?: AbortSignalLike } = {}): Promise<void> {\n    if (!this.cancelPromise) {\n      this.cancelPromise = this.cancelOnce(options);\n    } else if (options.abortSignal) {\n      throw new Error(\"A cancel request is currently pending\");\n    }\n    return this.cancelPromise;\n  }\n\n  /**\n   * Returns the state of the operation.\n   *\n   * Even though TState will be the same type inside any of the methods of any extension of the Poller class,\n   * implementations of the pollers can customize what's shared with the public by writing their own\n   * version of the `getOperationState` method, and by defining two types, one representing the internal state of the poller\n   * and a public type representing a safe to share subset of the properties of the internal state.\n   * Their definition of getOperationState can then return their public type.\n   *\n   * Example:\n   *\n   * ```ts\n   * // Let's say we have our poller's operation state defined as:\n   * interface MyOperationState extends PollOperationState<ResultType> {\n   *   privateProperty?: string;\n   *   publicProperty?: string;\n   * }\n   *\n   * // To allow us to have a true separation of public and private state, we have to define another interface:\n   * interface PublicState extends PollOperationState<ResultType> {\n   *   publicProperty?: string;\n   * }\n   *\n   * // Then, we define our Poller as follows:\n   * export class MyPoller extends Poller<MyOperationState, ResultType> {\n   *   // ... More content is needed here ...\n   *\n   *   public getOperationState(): PublicState {\n   *     const state: PublicState = this.operation.state;\n   *     return {\n   *       // Properties from PollOperationState<TResult>\n   *       isStarted: state.isStarted,\n   *       isCompleted: state.isCompleted,\n   *       isCancelled: state.isCancelled,\n   *       error: state.error,\n   *       result: state.result,\n   *\n   *       // The only other property needed by PublicState.\n   *       publicProperty: state.publicProperty\n   *     }\n   *   }\n   * }\n   * ```\n   *\n   * You can see this in the tests of this repository, go to the file:\n   * `../test/utils/testPoller.ts`\n   * and look for the getOperationState implementation.\n   */\n  public getOperationState(): TState {\n    return this.operation.state;\n  }\n\n  /**\n   * Returns the result value of the operation,\n   * regardless of the state of the poller.\n   * It can return undefined or an incomplete form of the final TResult value\n   * depending on the implementation.\n   */\n  public getResult(): TResult | undefined {\n    const state: PollOperationState<TResult> = this.operation.state;\n    return state.result;\n  }\n\n  /**\n   * Returns a serialized version of the poller's operation\n   * by invoking the operation's toString method.\n   */\n  public toString(): string {\n    return this.operation.toString();\n  }\n}\n"]}