{"inline_styles": [], "css_classes": [{"file": "admin\\carousel_list.html", "tag": "<thead class=\"table-dark\">", "class": "table-dark", "line_number": 28}, {"file": "admin\\view_role.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 37}, {"file": "admin\\view_user.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 93}, {"file": "admin\\system\\module_visibility.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 64}, {"file": "admin\\system\\module_visibility.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 66}, {"file": "admin\\system\\module_visibility.html", "tag": "<th class=\"w-60\">", "class": "w-60", "line_number": 67}, {"file": "area\\view_area.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 239}, {"file": "area\\view_area.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 239}, {"file": "area\\view_area.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 239}, {"file": "batch_flow\\form.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 28}, {"file": "consumption_plan\\create.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 40}, {"file": "consumption_plan\\create.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 40}, {"file": "consumption_plan\\create_from_weekly.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 61}, {"file": "consumption_plan\\super_editor.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 419}, {"file": "consumption_plan\\super_editor.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 419}, {"file": "consumption_plan\\super_editor.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 419}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"time-header\">", "class": "time-header", "line_number": 201}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\inspections_simple_table.html", "tag": "<th class=\"item-header\">", "class": "item-header", "line_number": 202}, {"file": "daily_management\\view_issue.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 65}, {"file": "daily_management\\view_issue.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 65}, {"file": "daily_management\\view_training.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 59}, {"file": "daily_management\\view_training.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 59}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 101}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 101}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 101}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 181}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 181}, {"file": "financial\\ledgers\\balance.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 181}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-period\">", "class": "uf-col-period", "line_number": 113}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-subject\">", "class": "uf-col-subject", "line_number": 114}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-type\">", "class": "uf-col-type", "line_number": 115}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-opening\">", "class": "uf-col-opening", "line_number": 116}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-debit\">", "class": "uf-col-debit", "line_number": 117}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-credit\">", "class": "uf-col-credit", "line_number": 118}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-ending\">", "class": "uf-col-ending", "line_number": 119}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-records\">", "class": "uf-col-records", "line_number": 120}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-generated\">", "class": "uf-col-generated", "line_number": 121}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-operations\">", "class": "uf-col-operations", "line_number": 122}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-line-no\">", "class": "uf-col-line-no", "line_number": 298}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-date sortable\" onclick=\"sortTable(1)\">", "class": "uf-col-date", "line_number": 299}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-voucher sortable\" onclick=\"sortTable(2)\">", "class": "uf-col-voucher", "line_number": 302}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-summary\">", "class": "uf-col-summary", "line_number": 305}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-debit sortable\" onclick=\"sortTable(4)\">", "class": "uf-col-debit", "line_number": 306}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-credit sortable\" onclick=\"sortTable(5)\">", "class": "uf-col-credit", "line_number": 309}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-balance sortable\" onclick=\"sortTable(6)\">", "class": "uf-col-balance", "line_number": 312}, {"file": "financial\\ledgers\\detail.html", "tag": "<th class=\"uf-col-operations\">", "class": "uf-col-operations", "line_number": 122}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-checkbox\">", "class": "uf-col-checkbox", "line_number": 153}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-code sortable\" onclick=\"sortTable(1)\">", "class": "uf-col-code", "line_number": 156}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-name sortable\" onclick=\"sortTable(2)\">", "class": "uf-col-name", "line_number": 159}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-type sortable\" onclick=\"sortTable(3)\">", "class": "uf-col-type", "line_number": 162}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-direction\">", "class": "uf-col-direction", "line_number": 165}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-opening sortable\" onclick=\"sortTable(5)\">", "class": "uf-col-opening", "line_number": 166}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-debit sortable\" onclick=\"sortTable(6)\">", "class": "uf-col-debit", "line_number": 169}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-credit sortable\" onclick=\"sortTable(7)\">", "class": "uf-col-credit", "line_number": 172}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-ending sortable\" onclick=\"sortTable(8)\">", "class": "uf-col-ending", "line_number": 175}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-col-operations\">", "class": "uf-col-operations", "line_number": 178}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 284}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 284}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 284}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-amount-col\">", "class": "uf-amount-col", "line_number": 284}, {"file": "financial\\ledgers\\general.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 150px;\">", "class": "uf-amount-col", "line_number": 377}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-voucher\">", "class": "col-voucher", "line_number": 57}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-subject-name\">", "class": "col-subject-name", "line_number": 58}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-date\">", "class": "col-date", "line_number": 59}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-amount\">", "class": "col-amount", "line_number": 60}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-voucher\">", "class": "col-voucher", "line_number": 57}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-date\">", "class": "col-date", "line_number": 59}, {"file": "financial\\payables\\pending_stock_ins.html", "tag": "<th class=\"col-actions\">", "class": "col-actions", "line_number": 63}, {"file": "financial\\reports\\income_statement.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 82}, {"file": "financial\\reports\\income_statement.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 82}, {"file": "financial\\reports\\income_statement.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 120px;\">", "class": "uf-amount-col", "line_number": 82}, {"file": "financial\\reports\\income_statement.html", "tag": "<th class=\"uf-amount-col\" style=\"width: 100px;\">", "class": "uf-amount-col", "line_number": 85}, {"file": "financial\\reports\\voucher_summary.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 115}, {"file": "financial\\reports\\voucher_summary.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 115}, {"file": "financial\\reports\\voucher_summary.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 115}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-sequence\">", "class": "uf-col-sequence", "line_number": 392}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-summary\">", "class": "uf-col-summary", "line_number": 393}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-subject\">", "class": "uf-col-subject", "line_number": 394}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-debit\">", "class": "uf-col-debit", "line_number": 395}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-credit\">", "class": "uf-col-credit", "line_number": 396}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-operations\">", "class": "uf-col-operations", "line_number": 399}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-code\">", "class": "uf-col-code", "line_number": 574}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-name\">", "class": "uf-col-name", "line_number": 575}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-type\">", "class": "uf-col-type", "line_number": 576}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-level\">", "class": "uf-col-level", "line_number": 577}, {"file": "financial\\vouchers\\create.html", "tag": "<th class=\"uf-col-direction\">", "class": "uf-col-direction", "line_number": 578}, {"file": "financial\\vouchers\\edit_professional.html", "tag": "<thead class=\"thead-dark\">", "class": "thead-dark", "line_number": 365}, {"file": "financial\\vouchers\\print.html", "tag": "<th class=\"col-sequence\">", "class": "col-sequence", "line_number": 401}, {"file": "financial\\vouchers\\print.html", "tag": "<th class=\"col-summary\">", "class": "col-summary", "line_number": 402}, {"file": "financial\\vouchers\\print.html", "tag": "<th class=\"col-subject\">", "class": "col-subject", "line_number": 403}, {"file": "financial\\vouchers\\print.html", "tag": "<th class=\"col-debit\">", "class": "col-debit", "line_number": 404}, {"file": "financial\\vouchers\\print.html", "tag": "<th class=\"col-credit\">", "class": "col-credit", "line_number": 405}, {"file": "food_sample\\print_daily.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 106}, {"file": "food_sample\\print_daily.html", "tag": "<th class=\"w-25\">", "class": "w-25", "line_number": 107}, {"file": "food_sample\\print_daily.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 106}, {"file": "food_sample\\print_daily.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 106}, {"file": "food_sample\\print_daily.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 106}, {"file": "food_sample\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "food_sample\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "ingredient\\categories.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 52}, {"file": "ingredient\\index.html", "tag": "<th class=\"sortable number-col\">", "class": "number-col", "line_number": 91}, {"file": "ingredient\\index.html", "tag": "<th class=\"number-col sortable\">", "class": "number-col", "line_number": 98}, {"file": "ingredient\\index.html", "tag": "<th class=\"status-col\">", "class": "status-col", "line_number": 99}, {"file": "ingredient\\index.html", "tag": "<th class=\"action-col\">", "class": "action-col", "line_number": 100}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\turnover.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 164}, {"file": "ingredient\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "inventory\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 349}, {"file": "inventory\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 349}, {"file": "inventory\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 349}, {"file": "inventory\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 349}, {"file": "inventory\\ingredient.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 23}, {"file": "inventory\\statistics.html", "tag": "<thead class=\"table-dark\">", "class": "table-dark", "line_number": 201}, {"file": "inventory_alert\\batch_create_requisition.html", "tag": "<th class=\"w-25\">", "class": "w-25", "line_number": 41}, {"file": "inventory_alert\\batch_create_requisition.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 42}, {"file": "inventory_alert\\batch_create_requisition.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 42}, {"file": "inventory_alert\\batch_create_requisition.html", "tag": "<th class=\"w-20\">", "class": "w-20", "line_number": 44}, {"file": "inventory_alert\\batch_create_requisition.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 42}, {"file": "inventory_alert\\create_requisition.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 23}, {"file": "inventory_alert\\create_requisition.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 23}, {"file": "inventory_alert\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 23}, {"file": "inventory_alert\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 23}, {"file": "main\\dashboard.html", "tag": "<th class=\"amount-col sortable\">", "class": "amount-col", "line_number": 126}, {"file": "main\\dashboard.html", "tag": "<th class=\"status-col\">", "class": "status-col", "line_number": 127}, {"file": "main\\dashboard.html", "tag": "<th class=\"date-col sortable\">", "class": "date-col", "line_number": 128}, {"file": "main\\dashboard.html", "tag": "<th class=\"action-col\">", "class": "action-col", "line_number": 129}, {"file": "material_batch\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 79}, {"file": "material_batch\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 79}, {"file": "purchase_order\\create_from_menu.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 448}, {"file": "purchase_order\\index.html", "tag": "<th class=\"date-col sortable\">", "class": "date-col", "line_number": 330}, {"file": "purchase_order\\index.html", "tag": "<th class=\"date-col sortable\">", "class": "date-col", "line_number": 330}, {"file": "purchase_order\\index.html", "tag": "<th class=\"amount-col sortable\">", "class": "amount-col", "line_number": 332}, {"file": "purchase_order\\index.html", "tag": "<th class=\"status-col\">", "class": "status-col", "line_number": 333}, {"file": "purchase_order\\index.html", "tag": "<th class=\"action-col\">", "class": "action-col", "line_number": 334}, {"file": "purchase_order\\print.html", "tag": "<th class=\"w-35\">", "class": "w-35", "line_number": 246}, {"file": "purchase_order\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 247}, {"file": "purchase_order\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 247}, {"file": "purchase_order\\print.html", "tag": "<th class=\"w-25\">", "class": "w-25", "line_number": 249}, {"file": "purchase_order\\view.html", "tag": "<th class=\"sequence-column\">", "class": "sequence-column", "line_number": 431}, {"file": "purchase_order\\view.html", "tag": "<th class=\"ingredient-name-column\">", "class": "ingredient-name-column", "line_number": 432}, {"file": "purchase_order\\view.html", "tag": "<th class=\"quantity-column\">", "class": "quantity-column", "line_number": 433}, {"file": "purchase_order\\view.html", "tag": "<th class=\"unit-column\">", "class": "unit-column", "line_number": 434}, {"file": "purchase_order\\view.html", "tag": "<th class=\"supplier-column\">", "class": "supplier-column", "line_number": 435}, {"file": "purchase_order\\view.html", "tag": "<th class=\"status-info-column\">", "class": "status-info-column", "line_number": 436}, {"file": "stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 178}, {"file": "stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 178}, {"file": "stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 178}, {"file": "stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 178}, {"file": "stock_in\\batch_editor_simplified.html", "tag": "<thead class=\"thead-dark\">", "class": "thead-dark", "line_number": 578}, {"file": "stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 245}, {"file": "stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 245}, {"file": "stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 245}, {"file": "stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 245}, {"file": "stock_in\\batch_editor_step2.html", "tag": "<th width=\"15%\" class=\"bg-light\">", "class": "bg-light", "line_number": 312}, {"file": "stock_in\\batch_editor_step2.html", "tag": "<th width=\"15%\" class=\"bg-light\">", "class": "bg-light", "line_number": 312}, {"file": "stock_in\\batch_editor_step2.html", "tag": "<th width=\"35%\" class=\"bg-light\">", "class": "bg-light", "line_number": 314}, {"file": "stock_in\\by_ingredient.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 29}, {"file": "stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-20\">", "class": "w-20", "line_number": 313}, {"file": "stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-20\">", "class": "w-20", "line_number": 313}, {"file": "stock_in\\edit.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 140}, {"file": "stock_in\\edit.html", "tag": "<th class=\"bg-light text-primary\">", "class": "bg-light", "line_number": 438}, {"file": "stock_in\\edit.html", "tag": "<th class=\"bg-light text-primary\">", "class": "text-primary", "line_number": 438}, {"file": "stock_in\\edit.html", "tag": "<th class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 442}, {"file": "stock_in\\edit.html", "tag": "<th class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 442}, {"file": "stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "stock_in\\edit.html", "tag": "<th width=\"8%\" class=\"bg-light text-primary\">", "class": "bg-light", "line_number": 748}, {"file": "stock_in\\edit.html", "tag": "<th width=\"8%\" class=\"bg-light text-primary\">", "class": "text-primary", "line_number": 748}, {"file": "stock_in\\edit.html", "tag": "<th width=\"10%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 752}, {"file": "stock_in\\edit.html", "tag": "<th width=\"10%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 752}, {"file": "stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "stock_in\\form.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 322}, {"file": "stock_in\\form.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 322}, {"file": "stock_in\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 237}, {"file": "stock_in\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 237}, {"file": "stock_in\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 377}, {"file": "stock_in\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 377}, {"file": "stock_in\\view.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 606}, {"file": "stock_in\\view.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 1021}, {"file": "stock_out\\edit.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "stock_out_item\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 27}, {"file": "stock_out_item\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 27}, {"file": "stock_out_item\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 27}, {"file": "stock_out_item\\detail.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 27}, {"file": "storage_location\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "storage_location\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "supplier\\certificate_view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "supplier\\form.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 191}, {"file": "supplier\\index.html", "tag": "<th class=\"sortable number-col\">", "class": "number-col", "line_number": 118}, {"file": "supplier\\index.html", "tag": "<th class=\"status-col\">", "class": "status-col", "line_number": 125}, {"file": "supplier\\index.html", "tag": "<th class=\"action-col\">", "class": "action-col", "line_number": 126}, {"file": "supplier\\product_view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "supplier\\product_view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "supplier\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "supplier\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 31}, {"file": "templates\\stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 178}, {"file": "templates\\stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 178}, {"file": "templates\\stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 178}, {"file": "templates\\stock_in\\batch_editor.html", "tag": "<th width=\"20%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 178}, {"file": "templates\\stock_in\\batch_editor_simplified.html", "tag": "<thead class=\"thead-dark\">", "class": "thead-dark", "line_number": 538}, {"file": "templates\\stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 245}, {"file": "templates\\stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 245}, {"file": "templates\\stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 245}, {"file": "templates\\stock_in\\batch_editor_step1.html", "tag": "<th width=\"15%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 245}, {"file": "templates\\stock_in\\batch_editor_step2.html", "tag": "<th width=\"15%\" class=\"bg-light\">", "class": "bg-light", "line_number": 312}, {"file": "templates\\stock_in\\batch_editor_step2.html", "tag": "<th width=\"15%\" class=\"bg-light\">", "class": "bg-light", "line_number": 312}, {"file": "templates\\stock_in\\batch_editor_step2.html", "tag": "<th width=\"35%\" class=\"bg-light\">", "class": "bg-light", "line_number": 314}, {"file": "templates\\stock_in\\by_ingredient.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 29}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-20\">", "class": "w-20", "line_number": 313}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 314}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "tag": "<th class=\"w-20\">", "class": "w-20", "line_number": 313}, {"file": "templates\\stock_in\\edit.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 140}, {"file": "templates\\stock_in\\edit.html", "tag": "<th class=\"bg-light text-primary\">", "class": "bg-light", "line_number": 438}, {"file": "templates\\stock_in\\edit.html", "tag": "<th class=\"bg-light text-primary\">", "class": "text-primary", "line_number": 438}, {"file": "templates\\stock_in\\edit.html", "tag": "<th class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 442}, {"file": "templates\\stock_in\\edit.html", "tag": "<th class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 442}, {"file": "templates\\stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "templates\\stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "templates\\stock_in\\edit.html", "tag": "<th width=\"8%\" class=\"bg-light text-primary\">", "class": "bg-light", "line_number": 748}, {"file": "templates\\stock_in\\edit.html", "tag": "<th width=\"8%\" class=\"bg-light text-primary\">", "class": "text-primary", "line_number": 748}, {"file": "templates\\stock_in\\edit.html", "tag": "<th width=\"10%\" class=\"bg-light text-danger\">", "class": "bg-light", "line_number": 752}, {"file": "templates\\stock_in\\edit.html", "tag": "<th width=\"10%\" class=\"bg-light text-danger\">", "class": "text-danger", "line_number": 752}, {"file": "templates\\stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "templates\\stock_in\\edit.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 575}, {"file": "templates\\stock_in\\form.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 322}, {"file": "templates\\stock_in\\form.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 322}, {"file": "templates\\stock_in\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 237}, {"file": "templates\\stock_in\\print.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 237}, {"file": "templates\\stock_in\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 364}, {"file": "templates\\stock_in\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 364}, {"file": "templates\\stock_in\\view.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 593}, {"file": "templates\\stock_in\\view.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 1008}, {"file": "traceability\\batch_trace.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 79}, {"file": "traceability\\batch_trace.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 79}, {"file": "traceability\\ingredient_trace.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 38}, {"file": "trace_document\\upload.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 28}, {"file": "warehouse\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "warehouse\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 26}, {"file": "weekly_menu\\1.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 423}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"id-column\">", "class": "id-column", "line_number": 222}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"week-column\">", "class": "week-column", "line_number": 223}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"status-column\">", "class": "status-column", "line_number": 224}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"creator-column\">", "class": "creator-column", "line_number": 225}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"time-column\">", "class": "time-column", "line_number": 226}, {"file": "weekly_menu\\index_v2.html", "tag": "<th class=\"actions-column\">", "class": "actions-column", "line_number": 227}, {"file": "weekly_menu\\plan.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 409}, {"file": "weekly_menu\\plan.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 410}, {"file": "weekly_menu\\plan.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 410}, {"file": "weekly_menu\\plan.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 410}, {"file": "weekly_menu\\plan_improved.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 57}, {"file": "weekly_menu\\plan_time_aware.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 251}, {"file": "weekly_menu\\plan_time_aware.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 252}, {"file": "weekly_menu\\plan_time_aware.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 252}, {"file": "weekly_menu\\plan_time_aware.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 252}, {"file": "weekly_menu\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\print_v2.html", "tag": "<th class=\"date-col\">", "class": "date-col", "line_number": 310}, {"file": "weekly_menu\\print_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 311}, {"file": "weekly_menu\\print_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 311}, {"file": "weekly_menu\\print_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 311}, {"file": "weekly_menu\\view.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 88}, {"file": "weekly_menu\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 89}, {"file": "weekly_menu\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 89}, {"file": "weekly_menu\\view.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 89}, {"file": "weekly_menu\\view_v2.html", "tag": "<th class=\"date-col\">", "class": "date-col", "line_number": 255}, {"file": "weekly_menu\\view_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 256}, {"file": "weekly_menu\\view_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 256}, {"file": "weekly_menu\\view_v2.html", "tag": "<th class=\"meal-col\">", "class": "meal-col", "line_number": 256}, {"file": "weekly_menu\\weekly_menu(new)\\plan_improved.html", "tag": "<th class=\"w-15\">", "class": "w-15", "line_number": 59}, {"file": "weekly_menu\\weekly_menu(new)\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\weekly_menu(new)\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\weekly_menu(new)\\print.html", "tag": "<th class=\"w-30\">", "class": "w-30", "line_number": 103}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "tag": "<thead class=\"thead-light\">", "class": "thead-light", "line_number": 128}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "tag": "<th class=\"w-15\" class=\"text-center\">", "class": "w-15", "line_number": 130}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "tag": "<th class=\"w-30\" class=\"text-center\">", "class": "w-30", "line_number": 131}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "tag": "<th class=\"w-30\" class=\"text-center\">", "class": "w-30", "line_number": 131}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "tag": "<th class=\"w-30\" class=\"text-center\">", "class": "w-30", "line_number": 131}], "style_blocks": [{"file": "base.html", "style": "display: flex;\n            height: 100vh;\n            width: 100%;\n            margin: 0;\n            padding: 0;\n            gap: 0;\n        }\n\n        /* 左侧导航栏 */\n        .sidebar {\n            width: 200px;\n            min-width: 200px;\n            max-width: 200px;\n            background: var(--theme-primary);\n            color: white;\n            display: flex;\n            flex-direction: column;\n            box-shadow: 2px 0 10px rgba(0,0,0,0.1);\n            transition: all 0.3s ease;\n            z-index: 1000;\n            margin: 0;\n            padding: 0;\n            border: none;\n            flex-shrink: 0;\n        }", "line_number": 92}, {"file": "base.html", "style": "flex: 1;\n            display: flex;\n            flex-direction: column;\n            overflow: hidden;\n            background: var(--bs-light, #f8f9fa);\n            margin: 0;\n            padding: 0;\n            border: none;\n            min-width: 0;\n        }\n\n        /* 顶部工具栏 */\n        .top-toolbar {\n            background: var(--theme-primary);\n            color: white;\n            border-bottom: 1px solid rgba(255,255,255,0.1);\n            padding: 0.75rem 1.5rem;\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            box-shadow: 0 2px 4px rgba(0,0,0,0.05);\n            z-index: 999;\n            transition: background-color 0.3s ease;\n        }", "line_number": 173}, {"file": "base.html", "style": "display: none !important;\n            }\n        }\n\n        /* 左右式布局特定样式 - 使用现有主题系统 */\n        /* 主题变量已在 theme-colors.css 中定义，这里只做布局相关的补充 */\n\n        .navbar-school-name {\n            font-size: 0.95rem;\n            font-weight: 500;\n            color: rgba(255, 255, 255, 0.95);\n            white-space: nowrap;\n            margin-top: 6px;\n            letter-spacing: 0.3px;\n            padding: 2px 8px;\n            background-color: rgba(255, 255, 255, 0.15);\n            border-radius: 10px;\n            border: 1px solid rgba(255, 255, 255, 0.2);\n        }", "line_number": 243}, {"file": "base_landing.html", "style": "width: 8px;\n        }\n        \n        ::-webkit-scrollbar-track {\n            background: #f1f1f1;\n        }", "line_number": 42}, {"file": "base_landing.html", "style": "::-webkit-scrollbar-thumb {\n            background: #165DFF;\n            border-radius: 4px;\n        }", "line_number": 48}, {"file": "base_landing.html", "style": "::-webkit-scrollbar-thumb:hover {\n            background: #0D47A1;\n        }", "line_number": 53}, {"file": "admin\\guide_management\\dashboard.html", "style": "position: absolute;\n    left: -35px;\n    top: 5px;\n    width: 10px;\n    height: 10px;\n    border-radius: 50%;\n}\n\n.timeline-item:not(:last-child)::before {\n    content: '';\n    position: absolute;\n    left: -31px;\n    top: 15px;\n    width: 2px;\n    height: calc(100% + 5px);\n    background-color: #dee2e6;\n}", "line_number": 297}, {"file": "admin\\system\\backups.html", "style": "font-weight: bold;\n    }\n    .backup-table th {\n        background-color: #f8f9fa;\n    }", "line_number": 12}, {"file": "components\\homepage_carousel.html", "style": "width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: 15px;\n}\n\n.homepage-carousel-container .carousel-caption {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));\n    padding: 40px 20px 20px;\n    text-align: left;\n    border-radius: 0 0 15px 15px;\n}", "line_number": 60}, {"file": "components\\homepage_carousel.html", "style": "width: 12px;\n    height: 12px;\n    border-radius: 50%;\n    margin: 0 4px;\n    background-color: rgba(255, 255, 255, 0.5);\n    border: 2px solid rgba(255, 255, 255, 0.8);\n    transition: all 0.3s ease;\n}\n\n.homepage-carousel-container .carousel-indicators .active {\n    background-color: #fff;\n    transform: scale(1.2);\n}", "line_number": 97}, {"file": "consumption_plan\\create_from_weekly.html", "style": "width: 100%;\n}\n\n/* 已存在消耗计划信息 */\n.existing-plan-info {\n    background-color: #f8f9fa;\n    padding: 6px 8px;\n    border-radius: 4px;\n    border-left: 3px solid #17a2b8;\n    margin-bottom: 8px;\n}", "line_number": 284}, {"file": "consumption_plan\\create_from_weekly.html", "style": "vertical-align: top;\n    padding: 10px 6px;\n    min-width: 180px;\n}\n\n.table th {\n    background-color: #f8f9fa;\n    font-weight: 600;\n    color: #495057;\n    border-bottom: 2px solid #dee2e6;\n    text-align: center;\n    padding: 8px 6px;\n}", "line_number": 373}, {"file": "consumption_plan\\index.html", "style": "font-size: 15px;\n}\n\n.table-compact th {\n    padding: 10px 8px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    font-weight: 600;\n    font-size: 14px;\n    color: white;\n    border: none;\n}", "line_number": 52}, {"file": "consumption_plan\\super_editor.html", "style": "margin: 15px 0;\n  }\n  .table th {\n    background-color: #f2f2f2;\n    position: sticky;\n    top: 0;\n    z-index: 10;\n  }", "line_number": 78}, {"file": "consumption_plan\\view.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n        }", "line_number": 67}, {"file": "consumption_plan\\view.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n        }", "line_number": 91}, {"file": "consumption_plan\\view.html", "style": "font-size: 16px;\n                color: #6c757d;\n            }\n\n            .info-table th {\n                background-color: #f8f9fa;\n                color: #495057;\n                font-size: 13px;\n            }", "line_number": 209}, {"file": "consumption_plan\\view.html", "style": "font-size: 14px;\n            }\n\n            .detail-table th {\n                background-color: #e9ecef;\n                color: #495057;\n                font-size: 13px;\n            }", "line_number": 220}, {"file": "consumption_plan\\view.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 256}, {"file": "consumption_plan\\view.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 263}, {"file": "daily_management\\add_training.html", "style": "position: absolute;\n        top: -10px;\n        right: -10px;\n        background: #dc3545;\n        color: white;\n        border-radius: 50%;\n        width: 24px;\n        height: 24px;\n        text-align: center;\n        line-height: 24px;\n        cursor: pointer;\n    }\n\n    .form-section {\n        background-color: #f8f9fc;\n        border-radius: 5px;\n        padding: 20px;\n        margin-bottom: 25px;\n        border-left: 4px solid #4e73df;\n    }", "line_number": 43}, {"file": "daily_management\\check_photos.html", "style": "width: 100%;\n        padding: 8px;\n        border: 1px solid #e3e6f0;\n        border-radius: 5px;\n        margin-bottom: 10px;\n        font-size: 0.9rem;\n    }\n\n    .submit-btn {\n        background: linear-gradient(135deg, #1cc88a 0%, #13855c 100%);\n        color: white;\n        border: none;\n        padding: 8px 15px;\n        border-radius: 5px;\n        font-weight: bold;\n        transition: all 0.3s ease;\n    }", "line_number": 124}, {"file": "daily_management\\companion_entry.html", "style": "max-width: 900px;\n            margin: 0 auto;\n            padding: 0 15px;\n        }\n\n        .welcome-card {\n            background: rgba(255, 255, 255, 0.95);\n            backdrop-filter: blur(10px);\n            border-radius: 20px;\n            box-shadow: 0 20px 40px rgba(0,0,0,0.1);\n            padding: 40px;\n            margin-bottom: 30px;\n            border: 1px solid rgba(255,255,255,0.2);\n        }", "line_number": 19}, {"file": "daily_management\\companion_entry.html", "style": "background: linear-gradient(45deg, #667eea, #764ba2);\n            border: none;\n            color: white;\n            padding: 15px 30px;\n            border-radius: 50px;\n            font-size: 16px;\n            font-weight: 600;\n            text-decoration: none;\n            display: inline-flex;\n            align-items: center;\n            justify-content: center;\n            transition: all 0.3s ease;\n            width: 100%;\n            margin-top: 20px;\n        }\n\n        .action-btn:hover {\n            background: linear-gradient(45deg, #5a6fd8, #6a4190);\n            color: white;\n            transform: scale(1.05);\n        }", "line_number": 90}, {"file": "daily_management\\edit_event.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 25}, {"file": "daily_management\\edit_issue.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 25}, {"file": "daily_management\\edit_training.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 25}, {"file": "daily_management\\events.html", "style": "background-color: #1cc88a;\n        color: white;\n    }\n\n    .event-other {\n        background-color: #f6c23e;\n        color: white;\n    }", "line_number": 24}, {"file": "daily_management\\inspections.html", "style": "border-collapse: separate;\n        border-spacing: 0;\n        width: 100%;\n        border: 1px solid #e3e6f0;\n        border-radius: 0.5rem;\n        overflow: hidden;\n    }\n\n    .inspection-table th {\n        background: linear-gradient(135deg, #f0f5ff, #f8f9fc);\n        font-weight: 600;\n        padding: 1rem 1.25rem;\n        border-bottom: 2px solid #e3e6f0;\n        color: #3a3b45;\n        font-size: 0.95rem;\n        letter-spacing: 0.5px;\n        text-transform: uppercase;\n    }", "line_number": 148}, {"file": "daily_management\\inspections.html", "style": "transform: scale(1.08);\n        border-color: #4e73df;\n        box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n        z-index: 1;\n    }\n\n    .photo-thumbnail::after {\n        content: '👁️';\n        position: absolute;\n        top: 50%;\n        left: 50%;\n        transform: translate(-50%, -50%) scale(0);\n        background-color: rgba(0,0,0,0.5);\n        color: white;\n        width: 100%;\n        height: 100%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        border-radius: 0.5rem;\n        opacity: 0;\n        transition: all 0.3s;\n    }", "line_number": 203}, {"file": "daily_management\\inspections_card_layout.html", "style": "flex: 1;\n        min-width: 300px;\n        border: 1px solid #e3e6f0;\n        border-radius: 0.35rem;\n        overflow: hidden;\n    }\n\n    .inspection-card-header {\n        background-color: #f8f9fc;\n        padding: 0.75rem 1rem;\n        border-bottom: 1px solid #e3e6f0;\n        display: flex;\n        justify-content: space-between;\n        align-items: center;\n    }", "line_number": 41}, {"file": "daily_management\\inspections_card_layout.html", "style": "padding: 0.75rem;\n        border: 1px solid #e3e6f0;\n    }\n\n    .inspection-table th {\n        background-color: #f8f9fc;\n        font-weight: 600;\n        text-align: center;\n    }", "line_number": 76}, {"file": "daily_management\\inspections_card_layout.html", "style": "display: inline-block;\n        padding: 0.25rem 0.5rem;\n        border-radius: 0.25rem;\n        font-size: 0.75rem;\n        font-weight: 600;\n        text-align: center;\n        min-width: 40px;\n    }\n\n    .status-badge.status-normal {\n        background-color: #1cc88a;\n        color: white;\n    }", "line_number": 99}, {"file": "daily_management\\inspections_simple_table.html", "style": "padding: 0.75rem;\n        border: 1px solid #36b9cc;\n        text-align: center;\n        vertical-align: middle;\n    }\n\n    .inspection-table th {\n        background-color: var(--theme-primary, #007bff);\n        font-weight: 600;\n        color: white;\n    }", "line_number": 42}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table th.time-header {\n        width: 80px;\n        background-color: var(--theme-primary, #007bff);\n    }", "line_number": 53}, {"file": "daily_management\\inspections_simple_table.html", "style": "background-color: #fff3e0;\n    }\n\n    .inspection-table td.item-cell:nth-child(2) {\n        background-color: #ffe0b2; /* 地面卫生 */\n    }", "line_number": 66}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table td.item-cell:nth-child(3) {\n        background-color: #c8e6c9; /* 操作台卫生 */\n    }", "line_number": 72}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table td.item-cell:nth-child(4) {\n        background-color: #b3e5fc; /* 设备卫生 */\n    }", "line_number": 76}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table td.item-cell:nth-child(5) {\n        background-color: #d1c4e9; /* 食材存储 */\n    }", "line_number": 80}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table td.item-cell:nth-child(6) {\n        background-color: #ffcdd2; /* 人员卫生 */\n    }", "line_number": 84}, {"file": "daily_management\\inspections_simple_table.html", "style": ".inspection-table td.item-cell:nth-child(7) {\n        background-color: #f8bbd0; /* 餐具消毒 */\n    }", "line_number": 88}, {"file": "daily_management\\inspections_table.html", "style": "width: 100%;\n        border-collapse: collapse;\n        margin-bottom: 1rem;\n        border: 1px solid #e3e6f0;\n    }\n\n    .inspection-table th {\n        background-color: #f8f9fc;\n        color: #4e73df;\n        font-weight: 600;\n        text-align: center;\n        padding: 0.75rem;\n        border: 1px solid #e3e6f0;\n    }", "line_number": 34}, {"file": "daily_management\\inspection_table_widget.html", "style": "width: 100%;\n            border-collapse: collapse;\n            margin-bottom: 1rem;\n            border: 1px solid #e3e6f0;\n            font-size: 0.9rem;\n        }\n        \n        .inspection-table th {\n            background-color: #f8f9fc;\n            color: #4e73df;\n            font-weight: 600;\n            text-align: center;\n            padding: 0.5rem;\n            border: 1px solid #e3e6f0;\n        }", "line_number": 5}, {"file": "daily_management\\inspection_table_widget.html", "style": "background-color: #f8f9fc;\n            font-weight: 600;\n            text-align: center;\n            width: 60px;\n        }\n        \n        /* 检查项目单元格样式 */\n        .inspection-cell {\n            background-color: #f9f3e8;\n            min-height: 100px;\n        }", "line_number": 28}, {"file": "daily_management\\issues.html", "style": "background-color: #1cc88a;\n        color: white;\n    }\n\n    .issue-other {\n        background-color: #f6c23e;\n        color: white;\n    }", "line_number": 51}, {"file": "daily_management\\photo_upload.html", "style": "width: 100%;\n            height: 100%;\n            object-fit: cover;\n        }\n\n        .preview-item .remove-btn {\n            position: absolute;\n            top: 0.25rem;\n            right: 0.25rem;\n            background-color: rgba(255, 255, 255, 0.8);\n            border-radius: 50%;\n            width: 1.5rem;\n            height: 1.5rem;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: #e74a3b;\n            cursor: pointer;\n        }", "line_number": 108}, {"file": "daily_management\\print_companion.html", "style": "display: inline-block;\n                width: 16px;\n                height: 16px;\n                background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\"><path fill=\"currentColor\" d=\"M259.3 17.8L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0z\"/></svg>');\n                background-repeat: no-repeat;\n                background-size: contain;\n            }\n            .star-empty {\n                background-image: url('data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 576 512\"><path fill=\"currentColor\" d=\"M528.1 171.5L382 150.2 316.7 17.8c-11.7-23.6-45.6-23.9-57.4 0L194 150.2 47.9 171.5c-26.2 3.8-36.7 36.1-17.7 54.6l105.7 103-25 145.5c-4.5 26.3 23.2 46 46.4 33.7L288 439.6l130.7 68.7c23.2 12.2 50.9-7.4 46.4-33.7l-25-145.5 105.7-103c19-18.5 8.5-50.8-17.7-54.6zM388.6 312.3l23.7 138.4L288 385.4l-124.3 65.3 23.7-138.4-100.6-98 139-20.2 62.2-126 62.2 126 139 20.2-100.6 98z\"/></svg>');\n            }", "line_number": 73}, {"file": "daily_management\\print_inspection_photo_detail.html", "style": "border: 1px solid #ddd;\n        padding: 8px 12px;\n    }\n    \n    .info-table th {\n        background-color: #f8f9fc;\n        text-align: right;\n        width: 30%;\n    }", "line_number": 66}, {"file": "daily_management\\print_log.html", "style": "border: 1px solid #000;\n                padding: 8px;\n                text-align: left;\n            }\n            .table th {\n                background-color: #f2f2f2;\n                font-weight: bold;\n            }", "line_number": 135}, {"file": "daily_management\\print_log.html", "style": "border: 1px solid #000;\n            padding: 8px;\n            text-align: left;\n        }\n        .table th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n        }", "line_number": 272}, {"file": "daily_management\\print_training.html", "style": "border: 1px solid #000;\n                padding: 8px;\n                text-align: left;\n            }\n            .table th {\n                background-color: #f2f2f2;\n                font-weight: bold;\n            }", "line_number": 135}, {"file": "daily_management\\print_training.html", "style": "border: 1px solid #000;\n            padding: 8px;\n            text-align: left;\n        }\n        .table th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n        }", "line_number": 269}, {"file": "daily_management\\public_rate_inspection_photos.html", "style": "background-color: #4e73df;\n        color: white;\n        border: none;\n        padding: 10px 20px;\n        border-radius: 5px;\n        font-weight: bold;\n        margin-top: 15px;\n        width: 100%;\n    }\n\n    .submit-rating:hover {\n        background-color: #2e59d9;\n    }", "line_number": 177}, {"file": "daily_management\\public_rate_photo.html", "style": "display: none;\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(0, 0, 0, 0.7);\n        z-index: 9999;\n        justify-content: center;\n        align-items: center;\n    }\n\n    .loading-content {\n        background-color: white;\n        padding: 30px;\n        border-radius: 15px;\n        text-align: center;\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n    }", "line_number": 189}, {"file": "daily_management\\public_upload_inspection_photo.html", "style": "width: 100%;\n        height: 120px;\n        object-fit: cover;\n        border-radius: 10px;\n    }\n\n    .photo-remove {\n        position: absolute;\n        top: 5px;\n        right: 5px;\n        background-color: #e74a3b;\n        color: white;\n        border: none;\n        border-radius: 50%;\n        width: 25px;\n        height: 25px;\n        font-size: 12px;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }", "line_number": 120}, {"file": "daily_management\\public_upload_inspection_photo.html", "style": "display: none;\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(0, 0, 0, 0.7);\n        z-index: 9999;\n        justify-content: center;\n        align-items: center;\n    }\n\n    .loading-content {\n        background-color: white;\n        padding: 30px;\n        border-radius: 15px;\n        text-align: center;\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n    }", "line_number": 230}, {"file": "daily_management\\public_upload_inspection_photo.html", "style": "width: 100%;\n        height: 8px;\n        background-color: #e3e6f0;\n        border-radius: 4px;\n        overflow: hidden;\n        margin-top: 15px;\n    }\n\n    .progress-fill {\n        height: 100%;\n        background: linear-gradient(90deg, #4e73df, #1cc88a);\n        width: 0%;\n        transition: width 0.3s ease;\n    }", "line_number": 257}, {"file": "daily_management\\public_upload_photo.html", "style": "width: 100%;\n        height: 120px;\n        object-fit: cover;\n        border-radius: 10px;\n    }\n\n    .photo-remove {\n        position: absolute;\n        top: 5px;\n        right: 5px;\n        background-color: #e74a3b;\n        color: white;\n        border: none;\n        border-radius: 50%;\n        width: 25px;\n        height: 25px;\n        font-size: 12px;\n        cursor: pointer;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }", "line_number": 120}, {"file": "daily_management\\public_upload_photo.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n\n    .history-info {\n        position: absolute;\n        bottom: 0;\n        left: 0;\n        right: 0;\n        background: rgba(0, 0, 0, 0.7);\n        color: white;\n        padding: 8px;\n        font-size: 0.8rem;\n    }", "line_number": 245}, {"file": "daily_management\\public_upload_photo.html", "style": "display: none;\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(0, 0, 0, 0.7);\n        z-index: 9999;\n        justify-content: center;\n        align-items: center;\n    }\n\n    .loading-content {\n        background-color: white;\n        padding: 30px;\n        border-radius: 15px;\n        text-align: center;\n        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);\n    }", "line_number": 273}, {"file": "daily_management\\public_upload_photo.html", "style": "width: 100%;\n        height: 8px;\n        background-color: #e3e6f0;\n        border-radius: 4px;\n        overflow: hidden;\n        margin-top: 15px;\n    }\n\n    .progress-fill {\n        height: 100%;\n        background: linear-gradient(90deg, #4e73df, #1cc88a);\n        width: 0%;\n        transition: width 0.3s ease;\n    }", "line_number": 300}, {"file": "daily_management\\simplified_inspection.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n\n    .photo-info {\n        padding: 0.5rem;\n        background-color: #f8f9fc;\n    }", "line_number": 57}, {"file": "daily_management\\view_event.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 19}, {"file": "daily_management\\view_issue.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 19}, {"file": "daily_management\\view_training.html", "style": "width: 100%;\n        height: 150px;\n        object-fit: cover;\n    }\n    .photo-caption {\n        padding: 8px;\n        background-color: #f8f9fc;\n        font-size: 0.8rem;\n    }", "line_number": 19}, {"file": "daily_management\\print\\base_print.html", "style": "border: 1px solid #000;\n            padding: 8px;\n            text-align: left;\n            vertical-align: top;\n        }\n        th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n        }", "line_number": 60}, {"file": "daily_management\\print\\base_print.html", "style": "width: 100%;\n                max-width: none;\n                padding: 0;\n            }\n        }\n        \n        /* 打印按钮样式 */\n        .print-button {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            padding: 10px 20px;\n            background-color: #4e73df;\n            color: white;\n            border: none;\n            border-radius: 5px;\n            cursor: pointer;\n            font-size: 14px;\n            box-shadow: 0 2px 5px rgba(0,0,0,0.2);\n        }", "line_number": 136}, {"file": "daily_management\\widgets\\image_widget.html", "style": "width: 200px;\n        height: 200px;\n        border: 2px dashed #ddd;\n        border-radius: 4px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n    }\n    .add-image-btn {\n        background: none;\n        border: none;\n        color: #6c757d;\n        display: flex;\n        flex-direction: column;\n        align-items: center;\n        cursor: pointer;\n    }", "line_number": 44}, {"file": "daily_management\\widgets\\image_widget.html", "style": "width: 80%;\n        margin-top: 10px;\n    }\n    .widget-container {\n        padding: 15px;\n        border: 1px solid #e3e6f0;\n        border-radius: 0.35rem;\n        background-color: #fff;\n    }", "line_number": 79}, {"file": "financial\\accounting_subjects\\text_tree.html", "style": "font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\n    font-size: 14px;\n    line-height: 1.6;\n    background-color: #fafafa;\n    border: 1px solid var(--uf-border);\n    padding: 16px;\n    white-space: pre-wrap;\n    overflow-x: auto;\n    min-height: 500px;\n    color: #333;\n}\n\n.uf-text-display.dark-theme {\n    background-color: #2d3748;\n    color: #e2e8f0;\n    border-color: #4a5568;\n}", "line_number": 115}, {"file": "financial\\assistant\\quick_create.html", "style": "content: '';\n    position: absolute;\n    top: 15px;\n    right: -50%;\n    width: 100%;\n    height: 2px;\n    background: rgba(255,255,255,0.3);\n}\n\n.step.active::after {\n    background: rgba(255,255,255,0.8);\n}", "line_number": 33}, {"file": "financial\\assistant\\quick_create.html", "style": "width: 30px;\n    height: 30px;\n    border-radius: 50%;\n    background: rgba(255,255,255,0.3);\n    color: white;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0 auto 8px;\n    font-weight: 600;\n    font-size: 14px;\n}\n\n.step.active .step-circle {\n    background: white;\n    color: #667eea;\n}", "line_number": 47}, {"file": "financial\\assistant\\quick_create.html", "style": "padding: 8px 12px;\n    border: 1px solid #e0e0e0;\n    text-align: left;\n    font-size: 13px;\n}\n\n.preview-table th {\n    background: #f8f9fa;\n    font-weight: 600;\n}", "line_number": 164}, {"file": "financial\\ledgers\\balance.html", "style": "border: 1px solid #999; padding: 4px 6px; text-align: center; }\n                th { background: #f0f8ff; font-weight: 600; }", "line_number": 520}, {"file": "financial\\ledgers\\balance.html", "style": "font-size: 11px;\n}\n\n.uf-balance-sheet-table th {\n    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);\n    color: var(--uf-primary);\n    font-weight: 600;\n    text-align: center;\n    white-space: nowrap;\n    position: relative;\n}", "line_number": 626}, {"file": "financial\\ledgers\\balance.html", "style": "cursor: pointer;\n    user-select: none;\n}\n\n.uf-balance-sheet-table th.sortable:hover {\n    background: linear-gradient(to bottom, #d9e8ff 0%, #b3d9ff 100%);\n}", "line_number": 639}, {"file": "financial\\ledgers\\balance.html", "style": "display: inline-block;\n    padding: 1px 4px;\n    font-size: 13px;\n    font-weight: 600;\n    border-radius: 2px;\n    border: 1px solid;\n    min-width: 16px;\n    text-align: center;\n}\n\n.uf-level-1 {\n    background: var(--uf-primary);\n    color: white;\n    border-color: var(--uf-primary);\n}", "line_number": 699}, {"file": "financial\\ledgers\\detail.html", "style": "width: 100%;\n    border-collapse: collapse;\n    font-family: var(--uf-font-family);\n    font-size: 13px;\n    background: var(--uf-white);\n    table-layout: fixed;\n}\n\n.uf-ledger-table th {\n    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);\n    color: white;\n    text-align: center;\n    vertical-align: middle;\n    font-weight: 600;\n    font-size: 13px;\n    border: 1px solid var(--uf-grid-border);\n    padding: 8px 6px;\n    height: 36px;\n    position: sticky;\n    top: 0;\n    z-index: 10;\n}", "line_number": 1110}, {"file": "financial\\ledgers\\detail.html", "style": "cursor: pointer;\n    user-select: none;\n    position: relative;\n}\n\n.uf-ledger-table th.sortable:hover {\n    background: linear-gradient(to bottom, var(--uf-primary-light) 0%, var(--uf-primary) 100%);\n}", "line_number": 1134}, {"file": "financial\\ledgers\\detail.html", "style": "background: var(--uf-row-hover) !important;\n}\n\n.uf-ledger-row:nth-child(even) {\n    background: var(--uf-gray-50);\n}", "line_number": 1175}, {"file": "financial\\ledgers\\detail.html", "style": "font-size: 10px !important;\n        page-break-inside: avoid;\n    }\n\n    .uf-ledger-table th {\n        font-size: 10px !important;\n        padding: 3px 4px !important;\n        background: #f0f0f0 !important;\n        color: #000 !important;\n    }", "line_number": 1443}, {"file": "financial\\ledgers\\detail.html", "style": "width: 100%;\n    border-collapse: collapse;\n    font-family: var(--uf-font-family);\n    font-size: 12px;\n    background: var(--uf-white);\n    table-layout: fixed;\n}\n\n.uf-generated-ledgers-table th {\n    background: linear-gradient(to bottom, var(--uf-primary) 0%, var(--uf-primary-dark) 100%);\n    color: white;\n    text-align: center;\n    vertical-align: middle;\n    font-weight: 600;\n    font-size: 12px;\n    border: 1px solid var(--uf-grid-border);\n    padding: 6px 4px;\n    height: 32px;\n    position: sticky;\n    top: 0;\n    z-index: 10;\n}", "line_number": 1542}, {"file": "financial\\ledgers\\general.html", "style": "border: 1px solid #999; padding: 4px 6px; text-align: center; }\n                th { background: #f0f8ff; font-weight: 600; }", "line_number": 825}, {"file": "financial\\ledgers\\general.html", "style": "width: 100%;\n    min-width: 980px;\n    border-collapse: collapse;\n    font-family: var(--uf-font-family);\n    font-size: 13px;\n    background: var(--uf-white);\n    table-layout: fixed;\n}\n\n.uf-general-ledger-table th {\n    background: var(--theme-primary, #007bff);\n    color: white;\n    font-weight: 600;\n    text-align: center;\n    white-space: nowrap;\n    position: sticky;\n    top: 0;\n    z-index: 10;\n    padding: 6px 4px;\n    height: 32px;\n    font-size: 12px;\n    border: 1px solid var(--theme-primary-dark, #0056b3);\n    overflow: hidden;\n}", "line_number": 1019}, {"file": "financial\\ledgers\\general.html", "style": "cursor: pointer;\n    user-select: none;\n    position: relative;\n}\n\n.uf-general-ledger-table th.sortable:hover {\n    background: var(--theme-primary-dark, #0056b3);\n}", "line_number": 1045}, {"file": "financial\\ledgers\\general.html", "style": "background: var(--uf-row-hover) !important;\n}\n\n.uf-ledger-row:nth-child(even) {\n    background: var(--uf-gray-50);\n}", "line_number": 1089}, {"file": "financial\\ledgers\\general.html", "style": "background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);\n    font-weight: 600;\n    border-top: 2px solid var(--uf-primary);\n}\n\n.uf-total-row th {\n    background: linear-gradient(to bottom, #e6f2ff 0%, #d9e8ff 100%);\n    color: var(--uf-primary);\n    font-weight: 700;\n    font-size: 13px;\n    padding: 6px 4px;\n    text-align: center;\n}", "line_number": 1318}, {"file": "financial\\ledgers\\general.html", "style": "font-size: 11px;\n}\n\n.uf-summary-table th {\n    background: linear-gradient(to bottom, #f0f8ff 0%, #e6f2ff 100%);\n}", "line_number": 1374}, {"file": "financial\\reports\\income_statement.html", "style": "border: 1px solid #999; padding: 4px 6px; text-align: center; }\n                th { background: #f0f8ff; font-weight: 600; }", "line_number": 500}, {"file": "financial\\reports\\income_statement.html", "style": "font-size: 13px;\n    width: 100%;\n}\n\n.uf-cost-analysis-table th {\n    background: linear-gradient(to bottom, #e6f2ff 0%, #cce5ff 100%);\n    color: var(--uf-primary);\n    font-weight: 600;\n    text-align: center;\n    white-space: nowrap;\n    position: relative;\n    padding: 8px 6px;\n    border: 1px solid #999;\n}", "line_number": 557}, {"file": "financial\\reports\\income_statement.html", "style": "cursor: pointer;\n    user-select: none;\n}\n\n.uf-cost-analysis-table th.sortable:hover {\n    background: linear-gradient(to bottom, #d9e8ff 0%, #b3d9ff 100%);\n}", "line_number": 573}, {"file": "financial\\vouchers\\index.html", "style": "background: linear-gradient(to bottom, #ffffff, #f5f5f5);\n    border: 1px solid #e0e0e0;\n    border-radius: 3px;\n    padding: 8px 16px; /* 增大内边距 */\n    font-size: 14px; /* 增大字体 */\n    color: #333;\n    cursor: pointer;\n    min-width: 80px; /* 增大最小宽度 */\n    text-align: center;\n    text-decoration: none;\n    display: inline-flex;\n    align-items: center;\n    gap: 6px; /* 增大图标和文字间距 */\n    height: 36px; /* 设置固定高度 */\n    font-weight: 500; /* 增加字体粗细 */\n}\n\n.toolbar-btn:hover {\n    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);\n    color: #1565c0;\n    text-decoration: none;\n}", "line_number": 44}, {"file": "financial\\vouchers\\index.html", "style": "background: linear-gradient(to bottom, #4caf50, #388e3c);\n    color: white;\n    border-color: #388e3c;\n    border-width: 2px; /* 更粗的边框 */\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 文字阴影 */\n}\n\n.toolbar-btn.create-btn:hover {\n    background: linear-gradient(to bottom, #388e3c, #2e7d32);\n    color: white;\n    border-color: #2e7d32;\n}", "line_number": 118}, {"file": "financial\\vouchers\\index.html", "style": "background: linear-gradient(to bottom, #ff9800, #f57c00);\n    color: white;\n    border-color: #f57c00;\n    border-width: 2px; /* 更粗的边框 */\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 文字阴影 */\n}\n\n.toolbar-btn.generate-btn:hover {\n    background: linear-gradient(to bottom, #f57c00, #ef6c00);\n    color: white;\n    border-color: #ef6c00;\n}", "line_number": 133}, {"file": "financial\\vouchers\\index.html", "style": "background: linear-gradient(to bottom, #9c27b0, #7b1fa2);\n    color: white;\n    border-color: #7b1fa2;\n    border-width: 2px; /* 更粗的边框 */\n    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); /* 文字阴影 */\n}\n\n.toolbar-btn.export-btn:hover {\n    background: linear-gradient(to bottom, #7b1fa2, #6a1b9a);\n    color: white;\n    border-color: #6a1b9a;\n}", "line_number": 148}, {"file": "financial\\vouchers\\index.html", "style": "position: relative;\n    background: white;\n    border-radius: 6px;\n    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);\n    max-width: 500px;\n    width: 90%;\n    max-height: 80vh;\n    overflow: hidden;\n    animation: modalSlideIn 0.3s ease-out;\n}\n\n.modal-header {\n    background: linear-gradient(to bottom, #1e88e5, #1565c0);\n    color: white;\n    padding: 15px 20px;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n}", "line_number": 321}, {"file": "financial\\vouchers\\index.html", "style": "width: 100%;\n    border-collapse: collapse;\n    font-size: 13px;\n    margin: 0;\n    table-layout: fixed;\n}\n\n.voucher-list-table th {\n    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);\n    border: 1px solid #90caf9;\n    padding: 6px 4px;\n    text-align: center;\n    font-weight: normal;\n    color: #1565c0;\n    font-size: 13px;\n    white-space: nowrap;\n}", "line_number": 418}, {"file": "financial\\vouchers\\index.html", "style": "display: inline-block;\n    padding: 2px 6px;\n    border-radius: 3px;\n    font-size: 13px;\n    font-weight: normal;\n    border: 1px solid;\n    min-width: 40px;\n    text-align: center;\n    white-space: nowrap;\n}\n\n.status-badge.draft {\n    background-color: #e0e0e0;\n    color: #616161;\n    border-color: #bdbdbd;\n}", "line_number": 483}, {"file": "financial\\vouchers\\index.html", "style": "display: flex;\n    flex-direction: row;\n    justify-content: center;\n    align-items: center;\n    gap: 5px;\n    width: 100px;\n    margin: 0 auto;\n    height: 100%;\n}\n\n.action-btn {\n    display: inline-flex;\n    width: 22px;\n    height: 22px;\n    align-items: center;\n    justify-content: center;\n    text-align: center;\n    background: #f8f8f8;\n    border: 1px solid #e0e0e0;\n    border-radius: 2px;\n    padding: 0;\n    margin: 0;\n    font-size: 13px;\n    color: #666;\n    cursor: pointer;\n    text-decoration: none;\n}", "line_number": 519}, {"file": "financial\\vouchers\\index.html", "style": "background: linear-gradient(to bottom, #ffffff, #f5f5f5);\n    border: 1px solid #e0e0e0;\n    border-radius: 3px;\n    padding: 4px 8px;\n    font-size: 13px;\n    color: #333;\n    cursor: pointer;\n    text-decoration: none;\n    min-width: 30px;\n    text-align: center;\n}\n\n.page-link:hover {\n    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);\n    color: #1565c0;\n    text-decoration: none;\n}", "line_number": 568}, {"file": "financial\\vouchers\\index.html", "style": "background-color: #fafbfc !important;\n    border-bottom: 1px solid #e9ecef !important;\n}\n\n.voucher-header-row th {\n    font-weight: 600 !important;\n    color: white !important;\n    background-color: var(--theme-primary, #007bff) !important;\n    border-right: 1px solid var(--theme-primary-light, #66b3ff) !important;\n    border: 1px solid var(--theme-primary-dark, #0056b3) !important;\n    font-size: 13px !important;\n}", "line_number": 836}, {"file": "financial\\vouchers\\index.html", "style": "border: 1px solid #000 !important;\n        padding: 0.25rem !important;\n    }\n}\n\n/* ========================================\n   关键样式覆盖 - 必须在最后加载以覆盖用友主题\n   ======================================== */\n\n/* 强制覆盖表头样式 - 最高优先级 */\n.voucher-detail-table .voucher-header-row th {\n    color: white !important;\n    background-color: var(--theme-primary, #007bff) !important;\n    font-weight: 600 !important;\n    font-size: 13px !important;\n    border: 1px solid var(--theme-primary-dark, #0056b3) !important;\n    border-right: 1px solid var(--theme-primary-light, #66b3ff) !important;\n}", "line_number": 963}, {"file": "financial\\vouchers\\index_v2.html", "style": "padding: 12px 15px;\n    text-align: left;\n    border-bottom: 1px solid #f0f0f0;\n}\n\n.voucher-table th {\n    background: #f8f9fa;\n    font-weight: 600;\n    color: #2c3e50;\n    position: sticky;\n    top: 0;\n    z-index: 10;\n}", "line_number": 153}, {"file": "financial\\vouchers\\index_v2.html", "style": "padding: 4px 8px;\n    border-radius: 12px;\n    font-size: 11px;\n    font-weight: 500;\n    text-align: center;\n    min-width: 60px;\n    display: inline-block;\n}\n\n.status-draft {\n    background: #fff3cd;\n    color: #856404;\n}", "line_number": 182}, {"file": "financial\\vouchers\\index_v2.html", "style": "width: 28px;\n    height: 28px;\n    border: none;\n    border-radius: 4px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    cursor: pointer;\n    font-size: 12px;\n    transition: all 0.3s ease;\n}\n\n.btn-view {\n    background: #e3f2fd;\n    color: #1976d2;\n}", "line_number": 223}, {"file": "financial\\vouchers\\index_v2.html", "style": "position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: rgba(0,0,0,0.5);\n    display: none;\n    align-items: center;\n    justify-content: center;\n    z-index: 9999;\n}\n\n.loading-content {\n    background: white;\n    padding: 30px;\n    border-radius: 8px;\n    text-align: center;\n}", "line_number": 326}, {"file": "financial\\vouchers\\pending_stock_ins.html", "style": "background: white;\n    border: 1px solid var(--yonyou-color-border);\n    border-radius: var(--yonyou-border-radius);\n    box-shadow: var(--yonyou-box-shadow);\n    margin: 0 auto;\n    max-width: 1400px;\n}\n\n.pending-stock-header {\n    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);\n    border-bottom: 1px solid #90caf9;\n    padding: 8px 15px;\n    font-size: 13px;\n    font-weight: bold;\n    color: #1565c0;\n    display: flex;\n    justify-content: space-between;\n    align-items: center;\n    border-radius: 4px 4px 0 0;\n}", "line_number": 21}, {"file": "financial\\vouchers\\pending_stock_ins.html", "style": "width: 100%;\n    border-collapse: collapse;\n    font-size: 13px;\n    margin: 0;\n    background: white;\n}\n\n.uf-table th {\n    background: linear-gradient(to bottom, #e3f2fd, #bbdefb);\n    border: 1px solid #90caf9;\n    padding: 8px 6px;\n    text-align: center;\n    font-weight: normal;\n    color: #1565c0;\n    font-size: 13px;\n    white-space: nowrap;\n}", "line_number": 151}, {"file": "financial\\vouchers\\print.html", "style": "width: 100%;\n            border-collapse: collapse;\n            border: 2px solid #000;\n            font-size: 12px;\n            table-layout: fixed;\n        }\n\n        .voucher-table th {\n            border: 1px solid #000;\n            padding: 6px 4px;\n            text-align: center;\n            vertical-align: middle;\n            background: #f8f8f8;\n            font-weight: bold;\n            height: 25px;\n        }", "line_number": 96}, {"file": "financial\\vouchers\\print.html", "style": "min-width: 60px;\n            padding: 2px 8px;\n            text-align: center;\n        }\n        \n        .print-controls {\n            text-align: center;\n            margin: 20px 0;\n            padding: 15px;\n            background: #f8f9fa;\n            border-radius: 5px;\n        }", "line_number": 254}, {"file": "financial\\vouchers\\text_view.html", "style": "font-family: 'Courier New', 'Monaco', 'Menlo', monospace;\n    font-size: 14px;\n    line-height: 1.4;\n    background-color: #f8f9fa;\n    border: 1px solid #dee2e6;\n    border-radius: 0.375rem;\n    padding: 20px;\n    white-space: pre-wrap;\n    overflow-x: auto;\n    min-height: 400px;\n  }\n  .text-display.dark-theme {\n    background-color: #2d3748;\n    color: #e2e8f0;\n    border-color: #4a5568;\n  }", "line_number": 11}, {"file": "food_sample\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n        }", "line_number": 53}, {"file": "food_sample\\print_daily.html", "style": "border: 1px solid #000;\n            padding: 8px;\n            text-align: center;\n        }\n        th {\n            background-color: #f2f2f2;\n        }", "line_number": 42}, {"file": "food_trace\\index.html", "style": "display: flex;\n        align-items: center;\n        width: 100%;\n        max-width: 600px;\n        margin-bottom: 12px;\n        padding: 16px 20px;\n        border-radius: 8px;\n        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);\n        box-shadow: 0 3px 8px rgba(0,0,0,0.12);\n        border-left: 4px solid #1e3c72;\n    }\n\n    .trace-icon {\n        width: 45px;\n        height: 45px;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);\n        color: white;\n        border-radius: 50%;\n        margin-right: 18px;\n        font-size: 18px;\n        box-shadow: 0 2px 6px rgba(30, 60, 114, 0.3);\n    }", "line_number": 452}, {"file": "food_trace\\qr_diagnosis.html", "style": "max-width: 200px;\n            border: 1px solid #ccc;\n            border-radius: 4px;\n            margin: 10px 0;\n        }\n        \n        .url-box {\n            background: #e9ecef;\n            padding: 10px;\n            border-radius: 4px;\n            font-family: monospace;\n            font-size: 12px;\n            word-break: break-all;\n            margin: 10px 0;\n        }", "line_number": 44}, {"file": "food_trace\\qr_result.html", "style": "max-width: 800px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 15px;\n            box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n            overflow: hidden;\n        }\n        \n        .trace-header {\n            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }", "line_number": 17}, {"file": "food_trace\\qr_test.html", "style": "max-width: 800px;\n            margin: 50px auto;\n            padding: 30px;\n            background: white;\n            border-radius: 10px;\n            box-shadow: 0 5px 15px rgba(0,0,0,0.1);\n        }\n        \n        .qr-display {\n            text-align: center;\n            padding: 30px;\n            background: #f8f9fa;\n            border-radius: 8px;\n            margin: 20px 0;\n        }", "line_number": 9}, {"file": "food_trace\\qr_test.html", "style": "max-width: 300px;\n            border: 2px solid #dee2e6;\n            border-radius: 8px;\n        }\n        \n        .url-display {\n            background: #e9ecef;\n            padding: 15px;\n            border-radius: 5px;\n            font-family: monospace;\n            word-break: break-all;\n            margin: 15px 0;\n        }", "line_number": 26}, {"file": "food_trace\\recipe_trace_result.html", "style": "max-width: 900px;\n            margin: 0 auto;\n            background: white;\n            border-radius: 15px;\n            box-shadow: 0 10px 30px rgba(0,0,0,0.2);\n            overflow: hidden;\n        }\n        \n        .trace-header {\n            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);\n            color: white;\n            padding: 30px;\n            text-align: center;\n        }", "line_number": 17}, {"file": "guide\\step_modal.html", "style": "width: 4px;\n}\n\n.video-list-scroll::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 2px;\n}", "line_number": 1040}, {"file": "guide\\step_modal.html", "style": ".video-list-scroll::-webkit-scrollbar-thumb {\n    background: #888;\n    border-radius: 2px;\n}", "line_number": 1047}, {"file": "guide\\step_modal.html", "style": ".video-list-scroll::-webkit-scrollbar-thumb:hover {\n    background: #555;\n}", "line_number": 1052}, {"file": "guide\\step_modal.html", "style": "width: 4px;\n}\n\n.guide-video-list::-webkit-scrollbar-track {\n    background: #f8f9fa;\n    border-radius: 2px;\n}", "line_number": 1142}, {"file": "guide\\step_modal.html", "style": ".guide-video-list::-webkit-scrollbar-thumb {\n    background: #dee2e6;\n    border-radius: 2px;\n}", "line_number": 1149}, {"file": "guide\\step_modal.html", "style": ".guide-video-list::-webkit-scrollbar-thumb:hover {\n    background: #adb5bd;\n}", "line_number": 1154}, {"file": "help\\financial.html", "style": "flex: 1;\n    min-width: 280px;\n}\n\n.uf-alert-warning {\n    background: #fff3cd;\n    border: 1px solid #ffeaa7;\n    border-radius: var(--uf-border-radius);\n    padding: 12px;\n    margin: 12px 0;\n    color: #856404;\n}", "line_number": 123}, {"file": "ingredient\\turnover.html", "style": "font-size: 15px;\n}\n\n.table-compact th {\n    padding: 10px 8px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    font-weight: 600;\n    font-size: 14px;\n    color: white;\n    border: none;\n}", "line_number": 17}, {"file": "inspection\\edit.html", "style": "transform: scale(1.05);\n        box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    }\n    \n    .table th {\n        background: linear-gradient(to right, #f8f9fc, #ffffff);\n        font-weight: 600;\n        padding: 0.75rem 1rem;\n        border-bottom: 2px solid #e3e6f0;\n    }", "line_number": 73}, {"file": "inspection\\index.html", "style": "font-size: 0.875rem;\n    }\n\n    .table th {\n        background: linear-gradient(to right, #f8f9fc, #ffffff);\n        font-weight: 600;\n        padding: 0.75rem 1rem;\n        border-bottom: 2px solid #e3e6f0;\n    }", "line_number": 68}, {"file": "inspection\\view.html", "style": "transform: scale(1.05);\n        box-shadow: 0 2px 8px rgba(0,0,0,0.1);\n    }\n    \n    .table th {\n        background: linear-gradient(to right, #f8f9fc, #ffffff);\n        font-weight: 600;\n        padding: 0.75rem 1rem;\n        border-bottom: 2px solid #e3e6f0;\n    }", "line_number": 68}, {"file": "inventory\\detail.html", "style": "font-size: 13px;\n}\n\n.table-compact th {\n    padding: 8px;\n    background: #f8f9fa;\n    font-weight: 600;\n    font-size: 12px;\n}", "line_number": 103}, {"file": "inventory\\index.html", "style": "font-size: 15px;\n}\n\n.table-compact th {\n    padding: 10px 8px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    font-weight: 600;\n    font-size: 14px;\n    color: white;\n    border: none;\n}", "line_number": 42}, {"file": "inventory\\index.html", "style": "display: inline-block;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    margin-right: 4px;\n}\n\n.stock-indicator.sufficient { background-color: #28a745; }", "line_number": 81}, {"file": "inventory\\item_label_print.html", "style": "width: 40px;\n            border-bottom: 1px solid #000;\n        }\n\n        .no-print {\n            position: fixed;\n            top: 20px;\n            right: 20px;\n            padding: 10px 20px;\n            background: #007bff;\n            color: #fff;\n            border: none;\n            border-radius: 4px;\n            cursor: pointer;\n        }", "line_number": 127}, {"file": "inventory\\print_inventory.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n            border: 1px solid #000;\n        }", "line_number": 66}, {"file": "inventory\\print_inventory.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            border: 1px solid #000;\n        }", "line_number": 91}, {"file": "inventory\\print_inventory.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #000 !important;\n            }", "line_number": 162}, {"file": "inventory\\print_inventory.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #000 !important;\n            }", "line_number": 169}, {"file": "main\\canteen_dashboard_new.html", "style": "max-width: 150px;\n        margin: 0 auto;\n    }\n    /* 加载状态样式 */\n    .loading-overlay {\n        position: absolute;\n        top: 0;\n        left: 0;\n        width: 100%;\n        height: 100%;\n        background-color: rgba(255, 255, 255, 0.7);\n        display: flex;\n        justify-content: center;\n        align-items: center;\n        z-index: 1000;\n        border-radius: 0.25rem;\n    }", "line_number": 107}, {"file": "main\\help.html", "style": "width: 8px;\n    }\n\n    ::-webkit-scrollbar-track {\n        background: #f1f1f1;\n    }", "line_number": 63}, {"file": "main\\help.html", "style": "::-webkit-scrollbar-thumb {\n        background: #888;\n        border-radius: 4px;\n    }", "line_number": 69}, {"file": "main\\help.html", "style": "::-webkit-scrollbar-thumb:hover {\n        background: #555;\n    }", "line_number": 74}, {"file": "main\\index.html", "style": "transform: translateY(-3px);\n            box-shadow: 0 15px 35px rgba(255,255,255,0.4);\n            color: var(--theme-primary);\n            text-decoration: none;\n        }\n\n        .btn-hero-outline {\n            background: transparent;\n            color: white;\n            border: 2px solid white;\n        }", "line_number": 156}, {"file": "main\\index.html", "style": "animation: fadeInUp 1.2s ease-out 0.5s both;\n        }\n\n        .website-url-card {\n            background: rgba(255, 255, 255, 0.95);\n            backdrop-filter: blur(10px);\n            border-radius: 20px;\n            padding: 25px 30px;\n            display: flex;\n            align-items: center;\n            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);\n            border: 2px solid rgba(255, 255, 255, 0.3);\n            transition: all 0.3s ease;\n            max-width: 600px;\n            margin: 0 auto;\n        }", "line_number": 200}, {"file": "main\\index.html", "style": "width: 55px;\n            height: 55px;\n            background: rgba(255, 255, 255, 0.2);\n            border: 2px solid rgba(255, 255, 255, 0.3);\n            border-radius: 50%;\n            color: white;\n            font-size: 1.3rem;\n            cursor: pointer;\n            transition: all 0.3s ease;\n            display: flex;\n            align-items: center;\n            justify-content: center;\n        }\n\n        .copy-btn-footer:hover {\n            background: rgba(255, 255, 255, 0.3);\n            border-color: rgba(255, 255, 255, 0.5);\n            transform: scale(1.1);\n            box-shadow: 0 8px 20px rgba(255, 255, 255, 0.2);\n        }", "line_number": 381}, {"file": "main\\index.html", "style": "transform: translateY(-5px);\n            box-shadow: var(--card-hover-shadow);\n        }\n\n        .video-thumbnail {\n            position: relative;\n            height: 200px;\n            background: var(--hero-gradient);\n            display: flex;\n            align-items: center;\n            justify-content: center;\n            color: white;\n            font-size: 3rem;\n            overflow: hidden;\n            border-radius: 15px 15px 0 0;\n        }", "line_number": 507}, {"file": "main\\index.html", "style": "transform: translateY(-2px);\n            box-shadow: 0 8px 25px rgba(var(--theme-primary-rgb), 0.4);\n        }\n\n        .pagination-btn:disabled {\n            background: #ccc;\n            cursor: not-allowed;\n            transform: none;\n            box-shadow: none;\n        }", "line_number": 566}, {"file": "product_batch\\adjust_products.html", "style": "overflow-y: auto;\n    max-height: 500px;\n}\n.table-fixed-header thead th {\n    position: sticky;\n    top: 0;\n    background-color: #f8f9fa;\n    z-index: 1;\n}", "line_number": 5}, {"file": "purchase_order\\create_form.html", "style": "width: 100%;\n  }\n\n  /* 修复颜色问题，使用系统主题 */\n  .card-header {\n    background-color: #f8f9fa !important;\n    border-bottom: 1px solid #dee2e6 !important;\n  }", "line_number": 22}, {"file": "purchase_order\\create_form.html", "style": "width: 6px;\n  }\n\n  .ingredients-grid::-webkit-scrollbar-track,\n  .ingredient-tabs-container::-webkit-scrollbar-track {\n    background: #f1f1f1;\n    border-radius: 3px;\n  }", "line_number": 191}, {"file": "purchase_order\\create_form.html", "style": ".ingredients-grid::-webkit-scrollbar-thumb,\n  .ingredient-tabs-container::-webkit-scrollbar-thumb {\n    background: #c1c1c1;\n    border-radius: 3px;\n  }", "line_number": 199}, {"file": "purchase_order\\create_form.html", "style": ".ingredients-grid::-webkit-scrollbar-thumb:hover,\n  .ingredient-tabs-container::-webkit-scrollbar-thumb:hover {\n    background: #a8a8a8;\n  }", "line_number": 205}, {"file": "purchase_order\\create_form.html", "style": "position: absolute !important;\n    width: 1px !important;\n    height: 1px !important;\n    padding: 0 !important;\n    margin: -1px !important;\n    overflow: hidden !important;\n    clip: rect(0, 0, 0, 0) !important;\n    white-space: nowrap !important;\n    border: 0 !important;\n  }\n\n  /* 供应商食材项目样式 */\n  .ingredient-item.supplier-item {\n    border-left: 3px solid #28a745;\n    background: linear-gradient(to right, rgba(40, 167, 69, 0.05), transparent);\n  }", "line_number": 213}, {"file": "purchase_order\\create_from_menu.html", "style": "position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: rgba(255,255,255,0.8);\n    display: none;\n    justify-content: center;\n    align-items: center;\n    z-index: 9999;\n}\n.quantity-input {\n    font-size: 1.2em !important;\n    font-weight: bold !important;\n    color: #dc3545 !important;\n    text-align: center !important;\n    background-color: #fff3f3 !important;\n    border: 2px solid #dc3545 !important;\n}", "line_number": 100}, {"file": "purchase_order\\create_from_menu.html", "style": "color: #495057;\n    font-weight: bold;\n    font-size: 1.1em;\n}\n.table thead th {\n    background-color: #f8f9fa;\n    font-weight: 600;\n}", "line_number": 140}, {"file": "purchase_order\\create_from_menu.html", "style": "width: 100%;\n}\n\n/* 无菜单容器 */\n.no-menu-container {\n    padding: 20px 8px;\n    text-align: center;\n    background-color: #f8f9fa;\n    border-radius: 4px;\n    border: 1px dashed #dee2e6;\n}", "line_number": 151}, {"file": "purchase_order\\create_from_menu.html", "style": "vertical-align: top;\n    padding: 10px 6px;\n    min-width: 180px;\n}\n\n.table th {\n    background-color: #f8f9fa;\n    font-weight: 600;\n    color: #495057;\n    border-bottom: 2px solid #dee2e6;\n    text-align: center;\n    padding: 8px 6px;\n}", "line_number": 211}, {"file": "purchase_order\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n        }", "line_number": 61}, {"file": "purchase_order\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n        }", "line_number": 88}, {"file": "purchase_order\\print.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 187}, {"file": "purchase_order\\print.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 194}, {"file": "purchase_order\\view.html", "style": "font-size: 0.875rem;\n    table-layout: fixed;\n    width: 100%;\n}\n\n.table-compact th {\n    font-weight: normal;\n    padding: 0.5rem;\n    border-top: none;\n    background-color: #f8f9fa;\n    color: #495057;\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n}", "line_number": 22}, {"file": "purchase_order\\view.html", "style": "content: '';\n    position: absolute;\n    left: -0.375rem;\n    top: 0.25rem;\n    width: 0.75rem;\n    height: 0.75rem;\n    border-radius: 50%;\n    background-color: #007bff;\n    border: 2px solid #fff;\n    box-shadow: 0 0 0 2px #e9ecef;\n}\n\n.purchase-order-timeline-item.pending::before {\n    background-color: #ffc107;\n}", "line_number": 142}, {"file": "recipe\\form_simplified.html", "style": "max-height: 200px;\n        max-width: 100%;\n        margin-top: 10px;\n    }\n    .user-defined-box {\n        border: 2px solid #ffc107; \n        padding: 15px; \n        border-radius: 5px; \n        background-color: #fff8e1;\n        margin-bottom: 15px;\n    }", "line_number": 25}, {"file": "stock_in\\batch_editor.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "stock_in\\batch_editor_simplified.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "stock_in\\batch_editor_simplified.html", "style": "display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    min-width: 200px;\n    position: relative;\n  }\n\n  .workflow-step.active .step-icon {\n    background-color: #007bff;\n    color: white;\n    border-color: #007bff;\n  }", "line_number": 263}, {"file": "stock_in\\batch_editor_step1.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n  \n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "stock_in\\batch_editor_step1.html", "style": "content: '';\n    position: absolute;\n    top: 50%;\n    right: 0;\n    width: 100%;\n    height: 2px;\n    background-color: #e3e6f0;\n    z-index: -1;\n  }\n  \n  .step-number {\n    display: inline-block;\n    width: 30px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 50%;\n    background-color: #f8f9fc;\n    border: 1px solid #e3e6f0;\n    margin-bottom: 5px;\n  }", "line_number": 109}, {"file": "stock_in\\batch_editor_step2.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "stock_in\\batch_editor_step2.html", "style": "content: '';\n    position: absolute;\n    top: 50%;\n    right: 0;\n    width: 100%;\n    height: 2px;\n    background-color: #e3e6f0;\n    z-index: -1;\n  }\n\n  .step-number {\n    display: inline-block;\n    width: 30px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 50%;\n    background-color: #f8f9fc;\n    border: 1px solid #e3e6f0;\n    margin-bottom: 5px;\n  }", "line_number": 93}, {"file": "stock_in\\create_from_purchase_order.html", "style": "position: relative;\n        z-index: 2;\n        text-align: center;\n        width: 20%;\n    }\n\n    .step-number {\n        width: 60px;\n        height: 60px;\n        line-height: 60px;\n        border-radius: 50%;\n        background-color: #e9ecef;\n        color: #6c757d;\n        font-size: 24px;\n        font-weight: bold;\n        margin: 0 auto 15px;\n        position: relative;\n    }", "line_number": 27}, {"file": "stock_in\\create_from_purchase_order.html", "style": "border-color: #80bdff;\n        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n    }\n\n    /* 表格样式 */\n    .table-items th {\n        background-color: #f8f9fa;\n        font-weight: 600;\n    }", "line_number": 94}, {"file": "stock_in\\index.html", "style": "font-size: 15px;\n}\n\n.table-compact th {\n    padding: 10px 8px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    font-weight: 600;\n    font-size: 14px;\n    color: white;\n    border: none;\n}", "line_number": 62}, {"file": "stock_in\\index.html", "style": "display: inline-block;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    margin-right: 4px;\n}\n\n.consumption-indicator.not-consumed { background-color: #28a745; }", "line_number": 90}, {"file": "stock_in\\labels_print.html", "style": "display: grid;\n            grid-template-columns: repeat(3, 1fr);\n            grid-gap: 5mm;\n            width: 100%;\n            max-width: 190mm; /* A4宽度减去边距 */\n        }\n\n        /* 单个标签样式 */\n        .label {\n            width: 60mm;\n            height: 40mm;\n            border: 2px solid #000;\n            padding: 3mm;\n            display: flex;\n            flex-direction: column;\n            justify-content: space-between;\n            page-break-inside: avoid;\n            background: white;\n            position: relative;\n        }", "line_number": 28}, {"file": "stock_in\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n        }", "line_number": 61}, {"file": "stock_in\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n        }", "line_number": 88}, {"file": "stock_in\\print.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 187}, {"file": "stock_in\\print.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 194}, {"file": "stock_in\\view.html", "style": "width: 80px;\n        height: 80px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 28px;\n        color: white;\n        margin: 0 auto 15px auto;\n        position: relative;\n    }\n\n    .workflow-circle.active {\n        background-color: #007bff;\n        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);\n    }", "line_number": 125}, {"file": "stock_in\\view.html", "style": "display: flex;\n        flex-direction: column;\n        align-items: center;\n        text-align: center;\n        min-width: 200px;\n        position: relative;\n    }\n\n    .workflow-step.active .step-icon {\n        background-color: #007bff;\n        color: white;\n        border-color: #007bff;\n    }", "line_number": 262}, {"file": "stock_in\\wizard.html", "style": "content: '';\n        position: absolute;\n        top: 25px;\n        left: 60%;\n        width: 80%;\n        height: 2px;\n        background-color: #e9ecef;\n        z-index: 1;\n    }\n\n    .step.active:not(:last-child):after,\n    .step.completed:not(:last-child):after {\n        background-color: #007bff;\n    }", "line_number": 20}, {"file": "stock_in\\wizard.html", "style": "width: 50px;\n        height: 50px;\n        border-radius: 50%;\n        background-color: #e9ecef;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0 auto;\n        position: relative;\n        z-index: 2;\n    }\n\n    .step.active .step-icon {\n        background-color: #007bff;\n        color: white;\n    }", "line_number": 36}, {"file": "stock_out\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n            border: 1px solid #000;\n        }", "line_number": 66}, {"file": "stock_out\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            border: 1px solid #000;\n        }", "line_number": 91}, {"file": "stock_out\\print.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #000 !important;\n            }", "line_number": 150}, {"file": "stock_out\\print.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #000 !important;\n            }", "line_number": 157}, {"file": "stock_out\\view.html", "style": "border: 1px solid #ddd;\n                        padding: 8px;\n                    }\n                    .info-table th {\n                        width: 25%;\n                        text-align: right;\n                        background-color: #f2f2f2;\n                    }", "line_number": 843}, {"file": "stock_out\\view.html", "style": "border: 1px solid #ddd;\n                        padding: 8px;\n                        text-align: center;\n                    }\n                    .items-table th {\n                        background-color: #f2f2f2;\n                    }", "line_number": 856}, {"file": "templates\\stock_in\\batch_editor.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "templates\\stock_in\\batch_editor_simplified.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "templates\\stock_in\\batch_editor_simplified.html", "style": "display: flex;\n    flex-direction: column;\n    align-items: center;\n    text-align: center;\n    min-width: 200px;\n    position: relative;\n  }\n\n  .workflow-step.active .step-icon {\n    background-color: #007bff;\n    color: white;\n    border-color: #007bff;\n  }", "line_number": 263}, {"file": "templates\\stock_in\\batch_editor_step1.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n  \n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "templates\\stock_in\\batch_editor_step1.html", "style": "content: '';\n    position: absolute;\n    top: 50%;\n    right: 0;\n    width: 100%;\n    height: 2px;\n    background-color: #e3e6f0;\n    z-index: -1;\n  }\n  \n  .step-number {\n    display: inline-block;\n    width: 30px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 50%;\n    background-color: #f8f9fc;\n    border: 1px solid #e3e6f0;\n    margin-bottom: 5px;\n  }", "line_number": 109}, {"file": "templates\\stock_in\\batch_editor_step2.html", "style": "background-color: #f8f9fa;\n    border-left: 4px solid #dc3545;\n    padding: 15px;\n    margin-bottom: 20px;\n  }\n\n  .batch-table th.bg-light {\n    background-color: #f8f9fa !important;\n    font-size: 1.1rem;\n    font-weight: bold;\n  }", "line_number": 51}, {"file": "templates\\stock_in\\batch_editor_step2.html", "style": "content: '';\n    position: absolute;\n    top: 50%;\n    right: 0;\n    width: 100%;\n    height: 2px;\n    background-color: #e3e6f0;\n    z-index: -1;\n  }\n\n  .step-number {\n    display: inline-block;\n    width: 30px;\n    height: 30px;\n    line-height: 30px;\n    border-radius: 50%;\n    background-color: #f8f9fc;\n    border: 1px solid #e3e6f0;\n    margin-bottom: 5px;\n  }", "line_number": 93}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "style": "position: relative;\n        z-index: 2;\n        text-align: center;\n        width: 20%;\n    }\n\n    .step-number {\n        width: 60px;\n        height: 60px;\n        line-height: 60px;\n        border-radius: 50%;\n        background-color: #e9ecef;\n        color: #6c757d;\n        font-size: 24px;\n        font-weight: bold;\n        margin: 0 auto 15px;\n        position: relative;\n    }", "line_number": 27}, {"file": "templates\\stock_in\\create_from_purchase_order.html", "style": "border-color: #80bdff;\n        box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);\n    }\n\n    /* 表格样式 */\n    .table-items th {\n        background-color: #f8f9fa;\n        font-weight: 600;\n    }", "line_number": 94}, {"file": "templates\\stock_in\\index.html", "style": "font-size: 15px;\n}\n\n.table-compact th {\n    padding: 10px 8px;\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    font-weight: 600;\n    font-size: 14px;\n    color: white;\n    border: none;\n}", "line_number": 62}, {"file": "templates\\stock_in\\index.html", "style": "display: inline-block;\n    width: 8px;\n    height: 8px;\n    border-radius: 50%;\n    margin-right: 4px;\n}\n\n.consumption-indicator.not-consumed { background-color: #28a745; }", "line_number": 90}, {"file": "templates\\stock_in\\labels_print.html", "style": "display: grid;\n            grid-template-columns: repeat(3, 1fr);\n            grid-gap: 5mm;\n            width: 100%;\n            max-width: 190mm; /* A4宽度减去边距 */\n        }\n\n        /* 单个标签样式 */\n        .label {\n            width: 60mm;\n            height: 40mm;\n            border: 2px solid #000;\n            padding: 3mm;\n            display: flex;\n            flex-direction: column;\n            justify-content: space-between;\n            page-break-inside: avoid;\n            background: white;\n            position: relative;\n        }", "line_number": 28}, {"file": "templates\\stock_in\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 8px;\n            vertical-align: top;\n            font-size: 10pt;\n        }\n\n        .info-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n            text-align: center;\n            width: 15%;\n        }", "line_number": 61}, {"file": "templates\\stock_in\\print.html", "style": "border: 1px solid #2c3e50;\n            padding: 6px;\n            text-align: center;\n            vertical-align: middle;\n            font-size: 10pt;\n        }\n\n        .items-table th {\n            background-color: white;\n            color: #000;\n            font-weight: bold;\n        }", "line_number": 88}, {"file": "templates\\stock_in\\print.html", "style": "display: table-footer-group; }\n\n            /* 分页后保持表头样式 */\n            .items-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 187}, {"file": "templates\\stock_in\\print.html", "style": ".info-table thead tr th {\n                background-color: white !important;\n                color: #000 !important;\n                border: 1px solid #2c3e50 !important;\n            }", "line_number": 194}, {"file": "templates\\stock_in\\view.html", "style": "width: 80px;\n        height: 80px;\n        border-radius: 50%;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        font-size: 28px;\n        color: white;\n        margin: 0 auto 15px auto;\n        position: relative;\n    }\n\n    .workflow-circle.active {\n        background-color: #007bff;\n        box-shadow: 0 4px 8px rgba(0, 123, 255, 0.3);\n    }", "line_number": 125}, {"file": "templates\\stock_in\\view.html", "style": "display: flex;\n        flex-direction: column;\n        align-items: center;\n        text-align: center;\n        min-width: 200px;\n        position: relative;\n    }\n\n    .workflow-step.active .step-icon {\n        background-color: #007bff;\n        color: white;\n        border-color: #007bff;\n    }", "line_number": 249}, {"file": "templates\\stock_in\\wizard.html", "style": "content: '';\n        position: absolute;\n        top: 25px;\n        left: 60%;\n        width: 80%;\n        height: 2px;\n        background-color: #e9ecef;\n        z-index: 1;\n    }\n\n    .step.active:not(:last-child):after,\n    .step.completed:not(:last-child):after {\n        background-color: #007bff;\n    }", "line_number": 20}, {"file": "templates\\stock_in\\wizard.html", "style": "width: 50px;\n        height: 50px;\n        border-radius: 50%;\n        background-color: #e9ecef;\n        display: flex;\n        align-items: center;\n        justify-content: center;\n        margin: 0 auto;\n        position: relative;\n        z-index: 2;\n    }\n\n    .step.active .step-icon {\n        background-color: #007bff;\n        color: white;\n    }", "line_number": 36}, {"file": "traceability\\trace_interface.html", "style": "width: 100%;\n        margin-top: 10px;\n    }\n    .trace-table th {\n        background-color: #f8f9fa;\n        padding: 8px;\n    }", "line_number": 67}, {"file": "traceability\\trace_interface_new.html", "style": "width: 100%;\n        margin-top: 10px;\n    }\n    .trace-table th {\n        background-color: #f8f9fa;\n        padding: 8px;\n    }", "line_number": 67}, {"file": "traceability\\trace_interface_simple.html", "style": "width: 100%;\n        margin-top: 10px;\n    }\n    .trace-table th {\n        background-color: #f8f9fa;\n        padding: 8px;\n    }", "line_number": 67}, {"file": "trace_document\\upload.html", "style": "position: absolute !important;\n        opacity: 0 !important;\n        width: 100% !important;\n        height: 100% !important;\n        cursor: pointer !important;\n        z-index: 1 !important;\n    }\n\n    .custom-file-label {\n        cursor: pointer;\n        border: 1px solid #ced4da;\n        border-radius: 0.25rem;\n        padding: 0.375rem 0.75rem;\n        background-color: #fff;\n        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;\n    }", "line_number": 179}, {"file": "weekly_menu\\1.html", "style": "position: relative;\n    width: auto;\n    margin: 0.5rem;\n    pointer-events: none;\n    max-width: 800px;\n    margin: 1.75rem auto;\n  }\n\n  .modal-content {\n    position: relative;\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    pointer-events: auto;\n    background-color: #fff;\n    background-clip: padding-box;\n    border: 1px solid rgba(0, 0, 0, 0.2);\n    border-radius: 0.3rem;\n    outline: 0;\n  }", "line_number": 116}, {"file": "weekly_menu\\index_v2.html", "style": "/* 表头样式重置和强制显示 */\n    #dataTable thead th {\n        background-color: var(--theme-primary, #007bff) !important;\n        background-image: none !important;\n        color: white !important;\n        font-weight: 600 !important;\n        font-size: 13px !important;\n        text-align: center !important;\n        border-bottom: 2px solid var(--theme-primary-dark, #0056b3) !important;\n        padding: 12px 8px !important;\n        vertical-align: middle !important;\n    }", "line_number": 4}, {"file": "weekly_menu\\index_v2.html", "style": "margin-bottom: 0;\n    }\n\n    .table thead th {\n        background-color: var(--theme-primary, #007bff) !important;\n        background-image: none !important;\n        border-bottom: 2px solid var(--theme-primary-dark, #0056b3) !important;\n        font-weight: 600 !important;\n        font-size: 13px !important;\n        color: white !important;\n        text-align: center !important;\n        vertical-align: middle !important;\n        position: relative;\n        z-index: 1;\n    }", "line_number": 148}, {"file": "weekly_menu\\index_v2.html", "style": "/* 确保表头文字可见 */\n    .table thead th::before {\n        content: '';\n        position: absolute;\n        top: 0;\n        left: 0;\n        right: 0;\n        bottom: 0;\n        background-color: var(--theme-primary, #007bff);\n        z-index: -1;\n    }", "line_number": 163}, {"file": "weekly_menu\\plan_v2.html", "style": "border: 1px solid #ddd;\n        padding: 8px;\n        text-align: center;\n    }\n\n    .menu-table th {\n        background-color: #f2f2f2;\n    }", "line_number": 116}, {"file": "weekly_menu\\plan_v2.html", "style": "border: 2px solid #000;\n            padding: 8px;\n            text-align: left;\n            vertical-align: top;\n            font-size: 11px;\n            line-height: 1.4;\n        }\n\n        .menu-table th {\n            background-color: #f0f0f0;\n            font-weight: bold;\n            text-align: center;\n            font-size: 12px;\n        }", "line_number": 318}, {"file": "weekly_menu\\plan_v2.html", "style": "width: 29.33%;\n            min-height: 80px;\n            position: relative;\n        }\n\n        /* 菜品显示样式 */\n        .menu-input {\n            border: none;\n            background: transparent;\n            padding: 0;\n            margin: 0;\n            min-height: auto;\n            font-size: 11px;\n            line-height: 1.4;\n            word-wrap: break-word;\n            white-space: pre-wrap;\n        }", "line_number": 343}, {"file": "weekly_menu\\print.html", "style": "border: 1px solid #000;\n            padding: 6px;\n            vertical-align: top;\n        }\n        th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n            text-align: center;\n        }", "line_number": 44}, {"file": "weekly_menu\\print_v2.html", "style": "border: 1px solid #000;\n            padding: 8px 6px;\n            vertical-align: top;\n            word-wrap: break-word;\n        }\n\n        .menu-table th {\n            background-color: #f5f5f5;\n            font-weight: bold;\n            text-align: center;\n            font-size: 13px;\n        }", "line_number": 73}, {"file": "weekly_menu\\print_v2.html", "style": "width: 30%;\n            min-width: 180px;\n        }\n\n        /* 日期单元格 */\n        .date-cell {\n            text-align: center;\n            font-weight: bold;\n            font-size: 11px;\n            background-color: #fafafa;\n        }", "line_number": 93}, {"file": "weekly_menu\\print_v2.html", "style": "border: 1.5px solid #000;\n                padding: 3px;\n            }\n\n            .menu-table th {\n                background-color: #e9e9e9;\n                font-size: 11px;\n            }", "line_number": 228}, {"file": "weekly_menu\\print_v2.html", "style": "background: white;\n                box-shadow: 0 0 10px rgba(0,0,0,0.1);\n                padding: 20px;\n                margin: 0 auto;\n                max-width: 1000px;\n            }\n\n            .print-button {\n                position: fixed;\n                top: 20px;\n                right: 20px;\n                background: #007bff;\n                color: white;\n                border: none;\n                padding: 10px 20px;\n                border-radius: 5px;\n                cursor: pointer;\n                font-size: 14px;\n                z-index: 1000;\n            }", "line_number": 260}, {"file": "weekly_menu\\view_v2.html", "style": "border: 1px solid #000;\n            padding: 8px 6px;\n            vertical-align: top;\n            word-wrap: break-word;\n        }\n\n        .menu-table th {\n            background-color: #f5f5f5;\n            font-weight: bold;\n            text-align: center;\n            font-size: 13px;\n        }", "line_number": 73}, {"file": "weekly_menu\\view_v2.html", "style": "width: 30%;\n            min-width: 180px;\n        }\n\n        /* 日期单元格 */\n        .date-cell {\n            text-align: center;\n            font-weight: bold;\n            font-size: 11px;\n            background-color: #fafafa;\n        }", "line_number": 93}, {"file": "weekly_menu\\weekly_menu(new)\\print.html", "style": "border: 1px solid #000;\n            padding: 6px;\n            vertical-align: top;\n        }\n        th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n            text-align: center;\n        }", "line_number": 44}, {"file": "weekly_menu\\weekly_menu(new)\\view.html", "style": "background-color: #d4edda;\n    color: #155724;\n    border: 1px solid #c3e6cb;\n  }\n\n  .status-other {\n    background-color: #e2e3e5;\n    color: #383d41;\n    border: 1px solid #d6d8db;\n  }", "line_number": 336}], "summary": {"total_files": 384, "files_with_headers": 237, "inline_bg_count": 0, "css_class_count": 307, "style_block_count": 230}}