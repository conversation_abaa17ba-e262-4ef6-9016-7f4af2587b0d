{"version": 3, "file": "tlsPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tlsPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAkBlC,8BAEC;AAfD,0EAGqD;AAErD;;GAEG;AACU,QAAA,aAAa,GAAG,wBAAgB,CAAC;AAE9C;;GAEG;AACH,SAAgB,SAAS,CAAC,WAAyB;IACjD,OAAO,IAAA,oBAAY,EAAC,WAAW,CAAC,CAAC;AACnC,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport type { TlsSettings } from \"../interfaces.js\";\n\nimport {\n  tlsPolicy as tspTlsPolicy,\n  tlsPolicyName as tspTlsPolicyName,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * Name of the TLS Policy\n */\nexport const tlsPolicyName = tspTlsPolicyName;\n\n/**\n * Gets a pipeline policy that adds the client certificate to the HttpClient agent for authentication.\n */\nexport function tlsPolicy(tlsSettings?: TlsSettings): PipelinePolicy {\n  return tspTlsPolicy(tlsSettings);\n}\n"]}