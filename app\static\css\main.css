/* 系统主样式文件 - 已清理Tailwind引用 */

/* 全局表格样式优化 - 专业版 */

/* 全局表头样式 - 覆盖所有可能的表头样式 */
th,
.table th,
.table thead th,
.table .thead-light th,
.table .thead-dark th,
.table-sm thead th,
.table-bordered thead th,
.table-striped thead th,
.table-hover thead th,
.thead-dark th,
.thead-light th,
table thead th,
table th,
.enterprise-table th,
.uf-table th,
.inspection-table th {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    color: white !important;
    background-color: var(--theme-primary, #007bff) !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    text-transform: none !important;
    letter-spacing: normal !important;
    border-color: var(--theme-primary-dark, #0056b3) !important;
}

/* 特别覆盖Bootstrap的thead-light样式 */
.table .thead-light th {
    color: white !important;
    background-color: var(--theme-primary, #007bff) !important;
    border-color: var(--theme-primary-dark, #0056b3) !important;
}

/* 表格内容样式 - 全部使用普通字体 */
.table td,
.table tbody td,
.table-sm td,
.table-sm tbody td,
.table-bordered td,
.table-striped td,
.table-hover td,
table td,
table tbody td {
    font-weight: normal !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 0.5rem !important;
    vertical-align: middle !important;
    border-top: 1px solid #e9ecef !important;
}

/* 表格内的链接也使用普通字体 */
.table td a,
.table tbody td a,
.table-sm td a,
.table-sm tbody td a,
table td a,
table tbody td a {
    font-weight: normal !important;
    text-decoration: none;
}

/* 表格内的强调文本也使用普通字体 */
.table td strong,
.table td b,
.table tbody td strong,
.table tbody td b,
.table-sm td strong,
.table-sm td b,
table td strong,
table td b {
    font-weight: 500 !important; /* 稍微加粗但不是黑体 */
}

/* 深色表头保持原有颜色但使用普通字体 */
.thead-dark th {
    background-color: #343a40 !important;
    color: #fff !important;
    border-color: #454d55 !important;
    font-weight: normal !important;
}

/* 表格行样式优化 */
.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 数字列专用样式 */
.table .number-column,
.table .amount-column {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
    font-weight: normal !important;
    text-align: right;
}

/* 状态列样式 */
.table .status-column {
    font-weight: normal !important;
}

/* 全局强制普通字体样式 - 覆盖所有可能的黑体 */
.table *,
.table-sm *,
.table-bordered *,
.table-striped *,
.table-hover *,
table * {
    font-weight: normal !important;
}

/* 特殊情况：稍微加粗但不是黑体 */
.table .semi-bold,
.table .medium-weight {
    font-weight: 500 !important;
}

/* Bootstrap徽章样式覆盖 */
.badge,
.badge-primary,
.badge-secondary,
.badge-success,
.badge-danger,
.badge-warning,
.badge-info,
.badge-light,
.badge-dark {
    font-weight: normal !important;
}

/* 按钮样式覆盖 */
.btn,
.btn-primary,
.btn-secondary,
.btn-success,
.btn-danger,
.btn-warning,
.btn-info,
.btn-light,
.btn-dark,
.btn-outline-primary,
.btn-outline-secondary,
.btn-outline-success,
.btn-outline-danger,
.btn-outline-warning,
.btn-outline-info,
.btn-outline-light,
.btn-outline-dark {
    font-weight: normal !important;
}

/* 链接样式覆盖 */
a, a:hover, a:focus, a:active {
    font-weight: normal !important;
}

/* 表单控件样式覆盖 */
.form-control,
.form-select,
.form-check-label,
.form-label,
label {
    font-weight: normal !important;
}

/* ========== 采购订单模块专用样式 ========== */

/* 采购订单状态样式 */
.purchase-order-status {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: normal !important;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
    transition: all 0.2s ease-in-out;
}

.purchase-order-status.status-pending,
.purchase-order-status.status-待确认 {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.purchase-order-status.status-confirmed,
.purchase-order-status.status-已确认 {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.purchase-order-status.status-delivered,
.purchase-order-status.status-准备入库 {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.purchase-order-status.status-cancelled,
.purchase-order-status.status-已取消 {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* 采购订单操作按钮组 */
.purchase-order-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.25rem;
    align-items: center;
}

.purchase-order-actions .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: normal !important;
    line-height: 1.2;
    border-radius: 0.2rem;
    transition: all 0.15s ease-in-out;
}

.purchase-order-actions .btn-sm:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* ========== 采购订单详情页面样式 ========== */

/* 订单状态样式 */
.order-status {
    font-size: 0.85em;
    padding: 0.25em 0.6em;
    border-radius: 0.25rem;
}

.status-pending {
    background-color: #ffc107;
    color: #000;
}

.status-confirmed {
    background-color: #17a2b8;
    color: #fff;
}

.status-delivered {
    background-color: #28a745;
    color: #fff;
}

.status-cancelled {
    background-color: #dc3545;
    color: #fff;
}

/* 时间线样式 */
.timeline {
    position: relative;
    padding: 20px 0;
}

.timeline::before {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 20px;
    width: 2px;
    background: #e9ecef;
}

.timeline-item {
    position: relative;
    padding-left: 50px;
    margin-bottom: 20px;
}

.timeline-item::before {
    content: '';
    position: absolute;
    left: 16px;
    top: 0;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: #007bff;
    border: 2px solid #fff;
}

.timeline-item.active::before {
    background: #28a745;
}

.timeline-content {
    padding: 10px 15px;
    background: #f8f9fa;
    border-radius: 4px;
}

.timeline-time {
    font-size: 0.85em;
    color: #6c757d;
}

/* 按钮状态样式 */
.btn-action-group {
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

.next-step-guide {
    margin-top: 15px;
    padding: 10px 15px;
    background-color: #e8f4ff;
    border-left: 4px solid #007bff;
    border-radius: 4px;
}

/* 流程步骤样式 */
.process-flow {
    display: flex;
    align-items: center;
    margin-top: 15px;
    overflow-x: auto;
    padding-bottom: 10px;
}

.process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px;
    min-width: 120px;
    text-align: center;
}

.process-step-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 5px;
}

.process-step-active .process-step-icon {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.process-step-completed .process-step-icon {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.process-connector {
    width: 30px;
    height: 2px;
    background-color: #dee2e6;
    margin: 0 5px;
}

.process-step-completed + .process-connector,
.process-step-active + .process-connector {
    background-color: #007bff;
}

/* 状态信息样式 */
.status-info-column {
    min-width: 200px;
}

.status-info-column .badge {
    display: block;
    margin-bottom: 2px;
    text-align: left;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 消耗状态行背景色 */
.table-light {
    background-color: #f8f9fa !important;
}

.table-warning {
    background-color: #fff3cd !important;
}

/* 状态徽章样式优化 */
.badge-consumed {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.badge-partial-consumed {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.badge-not-consumed {
    background-color: #f8f9fa;
    color: #6c757d;
    border: 1px solid #dee2e6;
}

/* 消耗餐次详情样式 */
.consumption-details {
    font-size: 0.7em;
    line-height: 1.2;
    color: #6c757d;
}

.consumption-details .meal-item {
    display: inline-block;
    margin-right: 8px;
    padding: 1px 4px;
    background-color: #e9ecef;
    border-radius: 2px;
    margin-bottom: 2px;
}

/* 横向流程样式 */
.compact-process-flow {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 30px 20px !important;
    position: relative !important;
}

.compact-process-flow::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 60px;
    right: 60px;
    height: 2px;
    background: #e9ecef;
    z-index: 1;
}

.compact-process-step {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
    cursor: pointer;
    flex: 0 0 auto;
}

.compact-step-icon {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 12px;
    font-size: 18px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    background: white;
}

.compact-process-step.completed .compact-step-icon {
    background: #28a745;
    color: white;
    border: 3px solid #28a745;
    box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
}

.compact-process-step.active .compact-step-icon {
    background: #007bff;
    color: white;
    border: 3px solid #007bff;
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
    animation: pulse 2s infinite;
}

.compact-process-step.pending .compact-step-icon {
    background: #f8f9fa;
    color: #6c757d;
    border: 3px solid #dee2e6;
}

.compact-step-title {
    font-size: 0.9rem;
    font-weight: 500 !important;
    margin-bottom: 4px;
    line-height: 1.2;
    color: #495057;
}

.compact-process-step.completed .compact-step-title {
    color: #28a745;
    font-weight: 600 !important;
}

.compact-process-step.active .compact-step-title {
    color: #007bff;
    font-weight: 600 !important;
}

.compact-step-time {
    font-size: 0.75rem;
    color: #6c757d;
    line-height: 1;
}

.compact-process-step.completed .compact-step-time {
    color: #28a745;
}

.compact-process-step.active .compact-step-time {
    color: #007bff;
}

@keyframes pulse {
    0% { box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3); }
    50% { box-shadow: 0 4px 20px rgba(0, 123, 255, 0.5); }
    100% { box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3); }
}

/* 订单状态卡片 */
.order-status-card .card {
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.border-left-primary {
    border-left-color: #007bff !important;
}

/* 快捷操作区域 */
.quick-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
}

.quick-action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 0.25rem;
}

.quick-action-item .btn {
    min-width: 120px;
    font-weight: 500 !important;
}

.quick-action-item small {
    color: #6c757d;
    font-size: 0.7rem;
}

/* 采购订单详情卡片 */
.purchase-order-detail-card {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    overflow: hidden;
    margin-bottom: 1rem;
    transition: box-shadow 0.15s ease-in-out;
}

.purchase-order-detail-card:hover {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.purchase-order-detail-card .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    padding: 0.75rem 1rem;
    font-weight: normal !important;
}

.purchase-order-detail-card .card-body {
    padding: 1rem;
}

/* 采购订单金额显示 */
.purchase-order-amount {
    font-size: 1.1rem;
    font-weight: 500 !important;
    color: #28a745;
}

.purchase-order-amount-large {
    font-size: 1.5rem;
    font-weight: 600 !important;
    color: #28a745;
}

/* 采购订单时间线 */
.purchase-order-timeline {
    position: relative;
    padding-left: 2rem;
}

.purchase-order-timeline::before {
    content: '';
    position: absolute;
    left: 0.75rem;
    top: 0;
    bottom: 0;
    width: 2px;
    background-color: #e9ecef;
}

.purchase-order-timeline-item {
    position: relative;
    margin-bottom: 1.5rem;
}

.purchase-order-timeline-item::before {
    content: '';
    position: absolute;
    left: -2.25rem;
    top: 0.25rem;
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
    background-color: #28a745;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #e9ecef;
}

.purchase-order-timeline-item.pending::before {
    background-color: #6c757d;
}

.purchase-order-timeline-item.active::before {
    background-color: #ffc107;
}

.purchase-order-timeline-title {
    font-weight: 500 !important;
    color: #495057;
    margin-bottom: 0.25rem;
}

.purchase-order-timeline-time {
    font-size: 0.875rem;
    color: #6c757d;
    font-weight: normal !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .compact-process-flow {
        padding: 20px 10px;
    }

    .compact-process-flow::before {
        left: 40px;
        right: 40px;
    }

    .compact-step-icon {
        width: 40px;
        height: 40px;
        font-size: 16px;
        margin-bottom: 8px;
    }

    .compact-step-title {
        font-size: 0.8rem;
    }

    .compact-step-time {
        font-size: 0.7rem;
    }
}

@media (max-width: 480px) {
    .compact-process-flow {
        flex-direction: column;
        padding: 20px 0;
    }

    .compact-process-flow::before {
        display: none;
    }

    .compact-process-step {
        flex-direction: row;
        text-align: left;
        width: 100%;
        padding: 15px;
        margin-bottom: 10px;
        background: #f8f9fa;
        border-radius: 8px;
    }

    .compact-step-icon {
        margin-right: 15px;
        margin-bottom: 0;
    }
}