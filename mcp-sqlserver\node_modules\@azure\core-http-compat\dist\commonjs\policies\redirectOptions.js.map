{"version": 3, "file": "redirectOptions.js", "sourceRoot": "", "sources": ["../../../src/policies/redirectOptions.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Options for how redirect responses are handled.\n */\nexport interface RedirectOptions {\n  /**\n   * When true, redirect responses are followed.  Defaults to true.\n   */\n  handleRedirects?: boolean;\n\n  /**\n   * The maximum number of times the redirect URL will be tried before\n   * failing.  Defaults to 20.\n   */\n  maxRetries?: number;\n}\n"]}