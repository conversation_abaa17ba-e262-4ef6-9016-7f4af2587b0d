#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头样式类扫描工具
扫描所有模板文件中的表头样式类，生成详细的Markdown报告
"""

import os
import re
from pathlib import Path
from datetime import datetime
from collections import defaultdict

def scan_table_headers():
    """扫描所有模板中的表头样式类"""
    
    templates_dir = Path("app/templates")
    results = {
        "files": [],           # 文件信息
        "th_classes": [],      # th标签的class
        "thead_classes": [],   # thead标签的class
        "table_classes": [],   # table标签的class
        "summary": {
            "total_files": 0,
            "files_with_tables": 0,
            "total_th_tags": 0,
            "total_thead_tags": 0,
            "total_table_tags": 0,
            "unique_th_classes": set(),
            "unique_thead_classes": set(),
            "unique_table_classes": set()
        }
    }
    
    # 匹配模式
    patterns = {
        # 表格相关标签
        "table_tags": re.compile(r'<table([^>]*)>', re.IGNORECASE),
        "thead_tags": re.compile(r'<thead([^>]*)>', re.IGNORECASE),
        "th_tags": re.compile(r'<th([^>]*)>', re.IGNORECASE),
        
        # class属性
        "class_attr": re.compile(r'class="([^"]*)"', re.IGNORECASE),
    }
    
    def process_file(file_path):
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        relative_path = str(file_path.relative_to(templates_dir))
        results["summary"]["total_files"] += 1
        
        file_info = {
            "path": relative_path,
            "table_tags": [],
            "thead_tags": [],
            "th_tags": [],
            "has_tables": False
        }
        
        # 查找table标签
        table_matches = patterns["table_tags"].findall(content)
        for table_attrs in table_matches:
            file_info["has_tables"] = True
            results["summary"]["total_table_tags"] += 1
            
            class_match = patterns["class_attr"].search(table_attrs)
            if class_match:
                classes = class_match.group(1).strip()
                if classes:
                    file_info["table_tags"].append(classes)
                    results["table_classes"].append({
                        "file": relative_path,
                        "classes": classes,
                        "line_number": content[:content.find(f'<table{table_attrs}>')].count('\n') + 1
                    })
                    results["summary"]["unique_table_classes"].add(classes)
        
        # 查找thead标签
        thead_matches = patterns["thead_tags"].findall(content)
        for thead_attrs in thead_matches:
            file_info["has_tables"] = True
            results["summary"]["total_thead_tags"] += 1
            
            class_match = patterns["class_attr"].search(thead_attrs)
            if class_match:
                classes = class_match.group(1).strip()
                if classes:
                    file_info["thead_tags"].append(classes)
                    results["thead_classes"].append({
                        "file": relative_path,
                        "classes": classes,
                        "line_number": content[:content.find(f'<thead{thead_attrs}>')].count('\n') + 1
                    })
                    results["summary"]["unique_thead_classes"].add(classes)
        
        # 查找th标签
        th_matches = patterns["th_tags"].findall(content)
        for th_attrs in th_matches:
            file_info["has_tables"] = True
            results["summary"]["total_th_tags"] += 1
            
            class_match = patterns["class_attr"].search(th_attrs)
            if class_match:
                classes = class_match.group(1).strip()
                if classes:
                    file_info["th_tags"].append(classes)
                    results["th_classes"].append({
                        "file": relative_path,
                        "classes": classes,
                        "line_number": content[:content.find(f'<th{th_attrs}>')].count('\n') + 1
                    })
                    results["summary"]["unique_th_classes"].add(classes)
        
        if file_info["has_tables"]:
            results["files"].append(file_info)
            results["summary"]["files_with_tables"] += 1
            return True
        
        return False
    
    # 遍历所有HTML文件
    for file_path in templates_dir.rglob("*.html"):
        process_file(file_path)
    
    return results

def generate_markdown_report(results):
    """生成Markdown报告"""
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 表头样式类扫描报告",
        "",
        f"**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 统计概览",
        "",
        f"- **扫描文件总数**: {results['summary']['total_files']}",
        f"- **包含表格的文件数**: {results['summary']['files_with_tables']}",
        f"- **table标签总数**: {results['summary']['total_table_tags']}",
        f"- **thead标签总数**: {results['summary']['total_thead_tags']}",
        f"- **th标签总数**: {results['summary']['total_th_tags']}",
        f"- **唯一table类数**: {len(results['summary']['unique_table_classes'])}",
        f"- **唯一thead类数**: {len(results['summary']['unique_thead_classes'])}",
        f"- **唯一th类数**: {len(results['summary']['unique_th_classes'])}",
        "",
    ])
    
    # 按文件分组的详细信息
    report_lines.extend([
        "## 📁 按文件分组的表头样式",
        "",
    ])
    
    for file_info in sorted(results["files"], key=lambda x: x["path"]):
        report_lines.append(f"### `{file_info['path']}`")
        report_lines.append("")
        
        if file_info["table_tags"]:
            report_lines.append("**Table标签样式类**:")
            for classes in file_info["table_tags"]:
                report_lines.append(f"- `{classes}`")
            report_lines.append("")
        
        if file_info["thead_tags"]:
            report_lines.append("**Thead标签样式类**:")
            for classes in file_info["thead_tags"]:
                report_lines.append(f"- `{classes}`")
            report_lines.append("")
        
        if file_info["th_tags"]:
            report_lines.append("**Th标签样式类**:")
            for classes in file_info["th_tags"]:
                report_lines.append(f"- `{classes}`")
            report_lines.append("")
        
        report_lines.append("---")
        report_lines.append("")
    
    # 按样式类分组的统计
    report_lines.extend([
        "## 🏷️ 按样式类分组的使用统计",
        "",
    ])
    
    # Table类统计
    if results["table_classes"]:
        report_lines.extend([
            "### Table标签样式类",
            "",
        ])
        
        table_class_counts = defaultdict(list)
        for item in results["table_classes"]:
            table_class_counts[item["classes"]].append(item["file"])
        
        for classes, files in sorted(table_class_counts.items()):
            report_lines.append(f"#### `{classes}` (使用 {len(files)} 次)")
            for file in sorted(set(files)):
                report_lines.append(f"- {file}")
            report_lines.append("")
    
    # Thead类统计
    if results["thead_classes"]:
        report_lines.extend([
            "### Thead标签样式类",
            "",
        ])
        
        thead_class_counts = defaultdict(list)
        for item in results["thead_classes"]:
            thead_class_counts[item["classes"]].append(item["file"])
        
        for classes, files in sorted(thead_class_counts.items()):
            report_lines.append(f"#### `{classes}` (使用 {len(files)} 次)")
            for file in sorted(set(files)):
                report_lines.append(f"- {file}")
            report_lines.append("")
    
    # Th类统计
    if results["th_classes"]:
        report_lines.extend([
            "### Th标签样式类",
            "",
        ])
        
        th_class_counts = defaultdict(list)
        for item in results["th_classes"]:
            th_class_counts[item["classes"]].append(item["file"])
        
        for classes, files in sorted(th_class_counts.items()):
            report_lines.append(f"#### `{classes}` (使用 {len(files)} 次)")
            for file in sorted(set(files)):
                report_lines.append(f"- {file}")
            report_lines.append("")
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始扫描表头样式类...")
    
    # 扫描文件
    results = scan_table_headers()
    
    # 生成报告
    print("📝 生成Markdown报告...")
    report_content = generate_markdown_report(results)
    
    # 保存报告
    report_file = "表头样式类扫描报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"✅ 报告已生成: {report_file}")
    print(f"📊 扫描了 {results['summary']['total_files']} 个文件")
    print(f"📋 找到 {results['summary']['files_with_tables']} 个包含表格的文件")
    print(f"🏷️  发现 {len(results['summary']['unique_th_classes'])} 个唯一的th样式类")

if __name__ == "__main__":
    main()
