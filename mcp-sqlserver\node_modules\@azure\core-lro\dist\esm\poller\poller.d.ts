import { AbortSignalLike } from "@azure/abort-controller";
import { BuildCreatePollerOptions, CreatePollerOptions, Operation, OperationState, SimplePollerLike } from "./models.js";
/**
 * Returns a poller factory.
 */
export declare function buildCreatePoller<TResponse, TResult, TState extends OperationState<TResult>>(inputs: BuildCreatePollerOptions<TResponse, TState>): (lro: Operation<TResponse, {
    abortSignal?: AbortSignalLike;
}>, options?: CreatePollerOptions<TResponse, TResult, TState>) => Promise<SimplePollerLike<TState, TResult>>;
//# sourceMappingURL=poller.d.ts.map