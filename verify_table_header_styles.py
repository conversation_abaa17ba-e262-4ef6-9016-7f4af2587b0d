#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头样式验证工具
验证强制表头主题色样式是否正确应用
"""

import re
from pathlib import Path
from datetime import datetime

def verify_theme_colors_css():
    """验证主题颜色CSS文件中的表头样式"""
    
    theme_file = Path("app/static/css/theme-colors.css")
    
    if not theme_file.exists():
        return False, "主题颜色文件不存在"
    
    try:
        with open(theme_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return False, f"读取主题文件失败: {e}"
    
    # 检查必要的CSS变量
    required_variables = [
        '--theme-table-header-bg',
        '--theme-table-header-color', 
        '--theme-table-header-border'
    ]
    
    missing_variables = []
    for var in required_variables:
        if var not in content:
            missing_variables.append(var)
    
    if missing_variables:
        return False, f"缺少必要的CSS变量: {', '.join(missing_variables)}"
    
    # 检查强制表头样式规则
    required_selectors = [
        'table thead',
        'table thead th',
        '.thead-light',
        '.thead-dark',
        'th[style*="background"]'
    ]
    
    missing_selectors = []
    for selector in required_selectors:
        if selector not in content:
            missing_selectors.append(selector)
    
    if missing_selectors:
        return False, f"缺少必要的CSS选择器: {', '.join(missing_selectors)}"
    
    # 检查!important规则
    important_count = content.count('!important')
    if important_count < 20:  # 应该有很多!important规则来强制覆盖
        return False, f"!important规则数量不足: {important_count}，可能无法有效覆盖现有样式"
    
    return True, f"主题颜色CSS验证通过，包含 {important_count} 个强制规则"

def check_css_loading_order():
    """检查CSS文件加载顺序"""
    
    base_template = Path("app/templates/base.html")
    
    if not base_template.exists():
        return False, "基础模板文件不存在"
    
    try:
        with open(base_template, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return False, f"读取基础模板失败: {e}"
    
    # 查找theme-colors.css的加载位置
    theme_css_pattern = r'<link[^>]*href="[^"]*theme-colors\.css[^"]*"[^>]*>'
    theme_matches = re.findall(theme_css_pattern, content)
    
    if not theme_matches:
        return False, "未找到theme-colors.css的加载"
    
    if len(theme_matches) > 1:
        return False, f"theme-colors.css被重复加载 {len(theme_matches)} 次"
    
    # 检查是否在其他CSS文件之后加载（确保能覆盖其他样式）
    lines = content.split('\n')
    theme_line = -1
    bootstrap_line = -1
    
    for i, line in enumerate(lines):
        if 'theme-colors.css' in line:
            theme_line = i
        if 'bootstrap' in line and '.css' in line:
            bootstrap_line = i
    
    if theme_line == -1:
        return False, "未找到theme-colors.css加载行"
    
    if bootstrap_line != -1 and theme_line < bootstrap_line:
        return False, "theme-colors.css应该在Bootstrap CSS之后加载以确保样式覆盖"
    
    return True, f"CSS加载顺序正确，theme-colors.css在第{theme_line + 1}行加载"

def generate_verification_report():
    """生成验证报告"""
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 表头主题色强制应用验证报告",
        "",
        f"**验证时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 验证结果",
        "",
    ])
    
    # 验证主题CSS文件
    css_valid, css_message = verify_theme_colors_css()
    report_lines.append(f"### 🎨 主题CSS文件验证")
    report_lines.append("")
    if css_valid:
        report_lines.append(f"✅ **通过**: {css_message}")
    else:
        report_lines.append(f"❌ **失败**: {css_message}")
    report_lines.append("")
    
    # 验证CSS加载顺序
    order_valid, order_message = check_css_loading_order()
    report_lines.append(f"### 📋 CSS加载顺序验证")
    report_lines.append("")
    if order_valid:
        report_lines.append(f"✅ **通过**: {order_message}")
    else:
        report_lines.append(f"❌ **失败**: {order_message}")
    report_lines.append("")
    
    # 总体状态
    overall_status = css_valid and order_valid
    report_lines.extend([
        "## 🎯 总体验证状态",
        "",
    ])
    
    if overall_status:
        report_lines.extend([
            "✅ **验证通过** - 强制表头主题色样式已正确配置",
            "",
            "### 🎉 预期效果",
            "",
            "- 所有表头背景色将强制使用当前主题的主色调",
            "- 表头文字颜色将强制为白色，确保可读性",
            "- 表头边框颜色将使用主题的深色调",
            "- 所有现有的灰色表头样式将被覆盖",
            "- 支持主题切换，表头颜色会跟随主题变化",
            "",
        ])
    else:
        report_lines.extend([
            "❌ **验证失败** - 强制表头主题色样式配置有问题",
            "",
            "### 🔧 需要修复的问题",
            "",
        ])
        
        if not css_valid:
            report_lines.append("- 修复主题CSS文件中的问题")
        if not order_valid:
            report_lines.append("- 调整CSS文件加载顺序")
        
        report_lines.append("")
    
    # 使用说明
    report_lines.extend([
        "## 📖 使用说明",
        "",
        "### 🎨 主题切换",
        "",
        "可以通过在HTML元素上设置 `data-theme` 属性来切换主题：",
        "",
        "```html",
        "<!-- 海洋蓝主题 -->",
        '<html data-theme="primary">',
        "",
        "<!-- 自然绿主题 -->", 
        '<html data-theme="success">',
        "",
        "<!-- 活力橙主题 -->",
        '<html data-theme="warning">',
        "```",
        "",
        "### 🔧 自定义表头颜色",
        "",
        "如果需要自定义表头颜色，可以修改CSS变量：",
        "",
        "```css",
        ":root {",
        "  --theme-table-header-bg: #your-color;",
        "  --theme-table-header-color: #ffffff;",
        "  --theme-table-header-border: #your-border-color;",
        "}",
        "```",
        "",
        "### 🚀 测试方法",
        "",
        "1. 打开任何包含表格的页面",
        "2. 检查表头背景色是否为主题色",
        "3. 检查表头文字是否为白色",
        "4. 尝试切换主题，观察表头颜色变化",
        "5. 使用浏览器开发者工具检查CSS规则应用情况",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始验证表头主题色强制应用...")
    
    # 验证主题CSS文件
    print("📋 验证主题CSS文件...")
    css_valid, css_message = verify_theme_colors_css()
    print(f"   {'✅' if css_valid else '❌'} {css_message}")
    
    # 验证CSS加载顺序
    print("📋 验证CSS加载顺序...")
    order_valid, order_message = check_css_loading_order()
    print(f"   {'✅' if order_valid else '❌'} {order_message}")
    
    # 总体状态
    overall_status = css_valid and order_valid
    print(f"\n🎯 总体验证状态: {'✅ 通过' if overall_status else '❌ 失败'}")
    
    # 生成报告
    print("\n📝 生成验证报告...")
    report_content = generate_verification_report()
    
    report_file = "表头主题色验证报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 验证报告已生成: {report_file}")
    
    if overall_status:
        print("\n🎉 恭喜！强制表头主题色样式已正确配置")
        print("   现在所有表头都将使用系统主题颜色")
        print("   可以打开 test_table_headers.html 进行测试")
    else:
        print("\n⚠️  配置有问题，请检查上述错误信息")

if __name__ == "__main__":
    main()
