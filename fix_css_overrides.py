#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复CSS覆盖问题工具
修复会覆盖系统表头样式的CSS规则
"""

import os
import re
from pathlib import Path

def fix_css_overrides():
    """修复CSS覆盖问题"""
    
    results = {
        "fixed_files": [],
        "changes": [],
        "summary": {
            "total_fixes": 0,
            "files_modified": 0
        }
    }
    
    # 需要修复的文件和规则
    fixes = [
        # 周菜单模块 - 高特异性选择器
        {
            "file": "app/templates/weekly_menu/index_v2.html",
            "pattern": r'#dataTable thead th\s*\{[^}]*background[^}]*\}',
            "replacement": '#dataTable thead th { background-color: var(--theme-primary, #007bff) !important; color: white !important; }',
            "description": "修复周菜单表头高特异性选择器"
        },
        
        # 批量编辑器
        {
            "file": "app/templates/stock_in/batch_editor.html",
            "pattern": r'\.batch-table th\.bg-light\s*\{[^}]*background[^}]*\}',
            "replacement": '.batch-table th.bg-light { background-color: var(--theme-primary, #007bff) !important; color: white !important; }',
            "description": "修复批量编辑器表头样式"
        },
        
        # 财务明细账
        {
            "file": "app/templates/financial/ledgers/detail.html",
            "pattern": r'\.uf-ledger-table th\s*\{[^}]*background[^}]*\}',
            "replacement": '.uf-ledger-table th { background-color: var(--theme-primary, #007bff) !important; color: white !important; }',
            "description": "修复财务明细账表头样式"
        },
        
        # 打印页面表头
        {
            "file": "app/templates/consumption_plan/view.html",
            "pattern": r'\.items-table thead tr th\s*\{[^}]*background[^}]*\}',
            "replacement": '.items-table thead tr th { background-color: var(--theme-primary, #007bff) !important; color: white !important; }',
            "description": "修复消耗计划打印表头样式"
        },
    ]
    
    def fix_file(file_path, pattern, replacement, description):
        """修复单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        # 使用正则表达式替换
        new_content = re.sub(pattern, replacement, content, flags=re.IGNORECASE | re.DOTALL)
        
        if new_content != content:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                results["changes"].append({
                    "file": str(file_path),
                    "description": description,
                    "pattern": pattern,
                    "replacement": replacement
                })
                
                results["summary"]["total_fixes"] += 1
                return True
            except Exception as e:
                print(f"写入文件失败 {file_path}: {e}")
                return False
        
        return False
    
    # 执行修复
    for fix in fixes:
        file_path = Path(fix["file"])
        if file_path.exists():
            if fix_file(file_path, fix["pattern"], fix["replacement"], fix["description"]):
                results["fixed_files"].append(str(file_path))
                results["summary"]["files_modified"] += 1
    
    return results

def fix_template_styles():
    """修复模板中的样式块"""
    
    # 需要修复的模板样式
    template_fixes = [
        # 周菜单模块
        {
            "file": "app/templates/weekly_menu/index_v2.html",
            "old_style": """#dataTable thead th {
        background-color: #e9ecef !important;
        background: none !important;
        color: #212529 !important;
    }""",
            "new_style": """#dataTable thead th {
        background-color: var(--theme-primary, #007bff) !important;
        color: white !important;
    }"""
        }
    ]
    
    for fix in template_fixes:
        file_path = Path(fix["file"])
        if file_path.exists():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                new_content = content.replace(fix["old_style"], fix["new_style"])
                
                if new_content != content:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f"✅ 修复模板样式: {file_path}")
            except Exception as e:
                print(f"❌ 修复模板失败 {file_path}: {e}")

def generate_report(results):
    """生成修复报告"""
    
    print("=" * 80)
    print("CSS覆盖问题修复报告")
    print("=" * 80)
    
    # 统计信息
    summary = results["summary"]
    print(f"\n📊 修复统计:")
    print(f"   修复文件数: {summary['files_modified']}")
    print(f"   总修复数: {summary['total_fixes']}")
    
    # 修复详情
    if results["changes"]:
        print(f"\n🔧 修复详情:")
        for change in results["changes"]:
            print(f"\n📁 {change['file']}:")
            print(f"   描述: {change['description']}")
            print(f"   模式: {change['pattern'][:50]}...")
            print(f"   替换: {change['replacement'][:50]}...")
    
    # 已修复的文件
    if results["fixed_files"]:
        print(f"\n✅ 已修复的文件:")
        for file_path in results["fixed_files"]:
            print(f"   - {file_path}")

def main():
    """主函数"""
    print("🔧 开始修复CSS覆盖问题...")
    
    # 确认操作
    response = input("\n⚠️  这将修改CSS和模板文件，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    # 修复CSS覆盖问题
    results = fix_css_overrides()
    
    # 修复模板样式
    print("\n🎨 修复模板样式...")
    fix_template_styles()
    
    # 生成报告
    generate_report(results)
    
    print("\n✅ 修复完成！")
    
    # 建议
    print("\n💡 后续建议:")
    print("   1. 测试所有页面的表头显示效果")
    print("   2. 检查主题切换功能是否正常")
    print("   3. 验证打印页面的表头样式")
    print("   4. 如有问题可通过Git回滚")

if __name__ == "__main__":
    main()
