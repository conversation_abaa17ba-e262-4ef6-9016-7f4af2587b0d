<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>布局测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
    <link href="http://xiaoyuanst.com/static/css/layout-optimization.css" rel="stylesheet">
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h3 class="card-title mb-0">测试页面标题</h3>
                            </div>
                            <div class="card-tools">
                                <a href="#" class="btn btn-secondary btn-sm">
                                    <i class="fas fa-arrow-left"></i> 返回列表
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <p>这是一个测试页面，用来验证卡片头部布局是否正确。</p>
                        <p>H3标题和按钮应该在同一行，左右对齐。</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 测试没有优化的布局 -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">没有优化的标题</h3>
                    </div>
                    <div class="card-body">
                        <p>这个卡片头部没有使用flex布局，标题应该单独显示。</p>
                        <a href="#" class="btn btn-primary btn-sm">
                            <i class="fas fa-plus"></i> 添加
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
