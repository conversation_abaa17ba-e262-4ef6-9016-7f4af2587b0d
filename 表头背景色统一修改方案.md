# 表头背景色统一修改方案

## 📋 需求说明

将C:\StudentsCMSSP\app\templates文件夹中所有表头底色与系统的主题色相结合，实现统一的视觉效果。

## 🎯 修改目标

- **统一性**: 所有模块的表头使用相同的主题色
- **一致性**: 表头颜色随主题切换而变化
- **兼容性**: 保持现有功能不受影响

## 🔍 需要检查的模块

根据侧边栏菜单，需要检查以下模块的表头样式：

### 主要模块
- 🏠 **工作台** - 仪表板和概览页面
- 📅 **日常管理** - 检查记录、日志管理等
- 📊 **菜单规划** - 周菜单计划相关页面
- 🏪 **供应链** - 采购订单、入库管理等
- 👥 **人员管理** - 员工信息管理
- 📤 **出库管理** - 库存出库相关
- 📋 **消耗计划** - 食材消耗规划
- 🚚 **供应商** - 供应商信息管理
- 🛍️ **供应商产品** - 产品目录管理
- 📦 **仓库管理** - 仓库和存储位置
- 🔒 **质量安全** - 食品安全相关页面

## 🛠️ 技术实施方案

### 1. 全局CSS样式修改

**主要文件**:
- `app/static/css/main.css` ✅ 已修改
- `app/static/css/enterprise-table-fixes.css` ✅ 已修改
- `app/static/css/enterprise-table-enhanced.css` ✅ 已修改

**修改内容**:
```css
/* 统一表头样式 */
.table th,
.table thead th,
table thead th,
table th {
    background-color: var(--theme-primary, #007bff) !important;
    color: white !important;
    border-bottom: 1px solid var(--theme-primary-dark, #0056b3) !important;
}
```

### 2. 特殊模块样式修改

#### 财务模块
**文件**: `app/static/financial/css/yonyou-theme.css` ✅ 已修改
```css
.uf-table th {
    background: var(--theme-primary, #007bff);
    color: white;
    border-bottom: 2px solid var(--theme-primary-dark, #0056b3);
}
```

#### 日常管理模块
**文件**: `app/templates/daily_management/inspections_simple_table.html` ✅ 已修改
```css
.inspection-table th {
    background-color: var(--theme-primary, #007bff);
    color: white;
}
```

### 3. 需要检查的模板文件

#### 工作台模块
- `app/templates/main/index.html`
- `app/templates/main/dashboard.html`
- `app/templates/main/canteen_dashboard.html`

#### 日常管理模块
- `app/templates/daily_management/inspections.html`
- `app/templates/daily_management/logs.html`
- `app/templates/daily_management/events.html`
- `app/templates/daily_management/trainings.html`

#### 菜单规划模块
- `app/templates/weekly_menu/index_v2.html`
- `app/templates/weekly_menu/plan_v2.html`
- `app/templates/recipe/index.html`

#### 供应链模块
- `app/templates/purchase_order/index.html`
- `app/templates/stock_in/index.html`
- `app/templates/inventory/index.html`
- `app/templates/supplier/index.html`

#### 人员管理模块
- `app/templates/employee/index.html`
- `app/templates/admin/users.html`

#### 质量安全模块
- `app/templates/food_trace/index.html`
- `app/templates/food_sample/index.html`
- `app/templates/inspection/index.html`

## 🔧 检测工具

创建了 `check_table_headers.py` 工具来自动检测所有模板中的表头样式：

### 检测内容
1. **内联样式**: 直接在HTML标签中的 `style` 属性
2. **样式块**: `<style>` 标签内的CSS规则
3. **CSS类名**: 表头使用的特殊类名

### 使用方法
```bash
python check_table_headers.py
```

## 📝 修改清单

### ✅ 已完成
- [x] 全局CSS样式文件修改
- [x] 财务模块用友主题修改
- [x] 日常管理检查表格修改
- [x] 财务凭证页面表头修改

### 🔄 待处理
- [ ] 工作台模块表头检查
- [ ] 菜单规划模块表头检查
- [ ] 供应链模块表头检查
- [ ] 人员管理模块表头检查
- [ ] 质量安全模块表头检查
- [ ] 其他模块表头检查

## 🎨 主题色变量

系统支持的主题色：
- **海洋蓝**: `#007bff` (默认)
- **现代灰**: `#6c757d`
- **自然绿**: `#28a745`
- **活力橙**: `#ffc107`
- **优雅紫**: `#17a2b8`
- **深邃红**: `#dc3545`

## 🚀 实施步骤

1. **运行检测工具**: 识别所有需要修改的表头
2. **批量修改**: 按模块逐一修改表头样式
3. **测试验证**: 确保所有页面表头正常显示
4. **主题切换测试**: 验证表头颜色随主题变化

## ⚠️ 注意事项

1. **保持功能完整**: 修改样式时不影响现有功能
2. **兼容性考虑**: 确保在不同浏览器中正常显示
3. **移动端适配**: 检查移动端表头显示效果
4. **打印样式**: 确保打印时表头样式正确

## 📊 预期效果

修改完成后，所有模块的表头将：
- 使用统一的主题色作为背景
- 白色文字确保良好的对比度
- 随主题切换自动变化颜色
- 保持专业的企业级外观

## 🔍 常见表头样式模式

### 模式1: 内联样式
```html
<th style="background-color: #f8f9fa;">列标题</th>
```
**修改为**:
```html
<th style="background-color: var(--theme-primary, #007bff); color: white;">列标题</th>
```

### 模式2: CSS类样式
```css
.custom-table th {
    background-color: #e3f2fd;
    color: #2e59d9;
}
```
**修改为**:
```css
.custom-table th {
    background-color: var(--theme-primary, #007bff);
    color: white;
}
```

### 模式3: 特殊颜色表头
```css
.inspection-table th.item-header:nth-child(2) {
    background-color: #ffe0b2; /* 地面卫生 */
}
```
**修改为**:
```css
.inspection-table th.item-header:nth-child(2) {
    background-color: var(--theme-primary, #007bff);
}
```

## 🛡️ 质量保证

### 测试检查项
- [ ] 所有页面表头颜色统一
- [ ] 主题切换功能正常
- [ ] 文字对比度符合可访问性标准
- [ ] 移动端显示正常
- [ ] 打印样式正确
- [ ] 无JavaScript错误
- [ ] 页面加载性能正常

### 回滚方案
如发现问题，可通过以下方式快速回滚：
1. 恢复备份的CSS文件
2. 移除主题色变量，使用固定颜色
3. 逐模块回滚，定位问题范围
