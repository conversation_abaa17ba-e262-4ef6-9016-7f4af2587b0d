"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.decompressResponsePolicyName = void 0;
exports.decompressResponsePolicy = decompressResponsePolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * The programmatic identifier of the decompressResponsePolicy.
 */
exports.decompressResponsePolicyName = policies_1.decompressResponsePolicyName;
/**
 * A policy to enable response decompression according to Accept-Encoding header
 * https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Accept-Encoding
 */
function decompressResponsePolicy() {
    return (0, policies_1.decompressResponsePolicy)();
}
//# sourceMappingURL=decompressResponsePolicy.js.map