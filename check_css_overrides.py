#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CSS样式覆盖检查工具
检查所有CSS文件和模板中的样式块，找出可能覆盖系统表头样式的规则
"""

import os
import re
from pathlib import Path

def check_css_overrides():
    """检查所有可能覆盖表头样式的CSS规则"""
    
    results = {
        "css_files": [],           # CSS文件中的规则
        "template_styles": [],     # 模板中的样式块
        "high_priority": [],       # 高优先级覆盖（!important）
        "specific_selectors": [],  # 特定选择器
        "summary": {
            "total_css_files": 0,
            "total_template_files": 0,
            "potential_overrides": 0,
            "important_rules": 0
        }
    }
    
    # 匹配模式
    patterns = {
        # 表头相关选择器
        "th_selectors": re.compile(r'([^{}]*th[^{}]*)\s*\{([^}]*)\}', re.IGNORECASE | re.DOTALL),
        "thead_selectors": re.compile(r'([^{}]*thead[^{}]*)\s*\{([^}]*)\}', re.IGNORECASE | re.DOTALL),
        "table_selectors": re.compile(r'([^{}]*table[^{}]*th[^{}]*)\s*\{([^}]*)\}', re.IGNORECASE | re.DOTALL),
        
        # 背景色和颜色属性
        "background_props": re.compile(r'background[^:]*:\s*([^;!]+)(!important)?', re.IGNORECASE),
        "color_props": re.compile(r'(?<!background-)color\s*:\s*([^;!]+)(!important)?', re.IGNORECASE),
        
        # 样式块
        "style_blocks": re.compile(r'<style[^>]*>(.*?)</style>', re.DOTALL | re.IGNORECASE),
        
        # 重要性标记
        "important": re.compile(r'!important', re.IGNORECASE),
    }
    
    def analyze_css_rule(selector, properties, file_path, line_num):
        """分析CSS规则"""
        rule_info = {
            "file": str(file_path),
            "line": line_num,
            "selector": selector.strip(),
            "properties": properties.strip(),
            "background_props": [],
            "color_props": [],
            "has_important": False,
            "specificity_score": 0
        }
        
        # 检查背景属性
        bg_matches = patterns["background_props"].findall(properties)
        for bg_value, important in bg_matches:
            rule_info["background_props"].append({
                "value": bg_value.strip(),
                "important": bool(important)
            })
            if important:
                rule_info["has_important"] = True
                results["summary"]["important_rules"] += 1
        
        # 检查颜色属性
        color_matches = patterns["color_props"].findall(properties)
        for color_value, important in color_matches:
            rule_info["color_props"].append({
                "value": color_value.strip(),
                "important": bool(important)
            })
            if important:
                rule_info["has_important"] = True
                results["summary"]["important_rules"] += 1
        
        # 计算选择器特异性分数（简化版）
        selector_lower = selector.lower()
        if '#' in selector_lower:
            rule_info["specificity_score"] += 100  # ID选择器
        if '.' in selector_lower:
            rule_info["specificity_score"] += 10   # 类选择器
        rule_info["specificity_score"] += selector_lower.count(' ') + 1  # 元素选择器
        
        return rule_info
    
    def process_css_file(file_path):
        """处理CSS文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取CSS文件失败 {file_path}: {e}")
            return
        
        results["summary"]["total_css_files"] += 1
        
        # 查找所有表头相关规则
        all_matches = []
        all_matches.extend(patterns["th_selectors"].findall(content))
        all_matches.extend(patterns["thead_selectors"].findall(content))
        all_matches.extend(patterns["table_selectors"].findall(content))
        
        for selector, properties in all_matches:
            # 只关注包含背景色或颜色的规则
            if any(prop in properties.lower() for prop in ['background', 'color']):
                line_num = content[:content.find(selector)].count('\n') + 1
                rule_info = analyze_css_rule(selector, properties, file_path, line_num)
                
                if rule_info["background_props"] or rule_info["color_props"]:
                    results["css_files"].append(rule_info)
                    results["summary"]["potential_overrides"] += 1
                    
                    # 高优先级规则
                    if rule_info["has_important"]:
                        results["high_priority"].append(rule_info)
                    
                    # 特定选择器
                    if rule_info["specificity_score"] > 20:
                        results["specific_selectors"].append(rule_info)
    
    def process_template_file(file_path):
        """处理模板文件中的样式块"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取模板文件失败 {file_path}: {e}")
            return
        
        results["summary"]["total_template_files"] += 1
        
        # 查找样式块
        style_blocks = patterns["style_blocks"].findall(content)
        for style_content in style_blocks:
            # 在样式块中查找表头规则
            all_matches = []
            all_matches.extend(patterns["th_selectors"].findall(style_content))
            all_matches.extend(patterns["thead_selectors"].findall(style_content))
            all_matches.extend(patterns["table_selectors"].findall(style_content))
            
            for selector, properties in all_matches:
                if any(prop in properties.lower() for prop in ['background', 'color']):
                    line_num = content[:content.find(style_content)].count('\n') + 1
                    rule_info = analyze_css_rule(selector, properties, file_path, line_num)
                    
                    if rule_info["background_props"] or rule_info["color_props"]:
                        results["template_styles"].append(rule_info)
                        results["summary"]["potential_overrides"] += 1
                        
                        if rule_info["has_important"]:
                            results["high_priority"].append(rule_info)
                        
                        if rule_info["specificity_score"] > 20:
                            results["specific_selectors"].append(rule_info)
    
    # 扫描CSS文件
    css_dirs = [
        Path("app/static/css"),
        Path("app/static/financial/css"),
        Path("app/static/vendor"),
    ]
    
    for css_dir in css_dirs:
        if css_dir.exists():
            for css_file in css_dir.rglob("*.css"):
                process_css_file(css_file)
    
    # 扫描模板文件
    templates_dir = Path("app/templates")
    if templates_dir.exists():
        for template_file in templates_dir.rglob("*.html"):
            process_template_file(template_file)
    
    return results

def generate_report(results):
    """生成检查报告"""
    
    print("=" * 80)
    print("CSS样式覆盖检查报告")
    print("=" * 80)
    
    # 统计信息
    summary = results["summary"]
    print(f"\n📊 统计信息:")
    print(f"   扫描CSS文件数: {summary['total_css_files']}")
    print(f"   扫描模板文件数: {summary['total_template_files']}")
    print(f"   潜在覆盖规则数: {summary['potential_overrides']}")
    print(f"   !important规则数: {summary['important_rules']}")
    
    # 高优先级覆盖
    if results["high_priority"]:
        print(f"\n🚨 高优先级覆盖规则 (!important):")
        for rule in results["high_priority"]:
            print(f"   📁 {rule['file']}:{rule['line']}")
            print(f"      选择器: {rule['selector']}")
            for bg in rule["background_props"]:
                if bg["important"]:
                    print(f"      背景色: {bg['value']} !important")
            for color in rule["color_props"]:
                if color["important"]:
                    print(f"      文字色: {color['value']} !important")
            print()
    
    # 特定选择器
    if results["specific_selectors"]:
        print(f"\n🎯 高特异性选择器 (可能覆盖全局样式):")
        for rule in sorted(results["specific_selectors"], key=lambda x: x["specificity_score"], reverse=True)[:10]:
            print(f"   📁 {rule['file']}:{rule['line']} (特异性: {rule['specificity_score']})")
            print(f"      选择器: {rule['selector']}")
            for bg in rule["background_props"]:
                print(f"      背景色: {bg['value']}")
            for color in rule["color_props"]:
                print(f"      文字色: {color['value']}")
            print()
    
    # CSS文件中的规则
    if results["css_files"]:
        print(f"\n📄 CSS文件中的表头样式规则:")
        css_by_file = {}
        for rule in results["css_files"]:
            file_name = Path(rule["file"]).name
            if file_name not in css_by_file:
                css_by_file[file_name] = []
            css_by_file[file_name].append(rule)
        
        for file_name, rules in css_by_file.items():
            print(f"   📁 {file_name} ({len(rules)} 个规则)")
            for rule in rules[:3]:  # 只显示前3个
                print(f"      - {rule['selector']}")
            if len(rules) > 3:
                print(f"      ... 还有 {len(rules) - 3} 个规则")
            print()

def main():
    """主函数"""
    print("🔍 开始检查CSS样式覆盖...")
    
    results = check_css_overrides()
    generate_report(results)
    
    print("\n✅ 检查完成！")
    
    # 建议
    print("\n💡 修改建议:")
    if results["high_priority"]:
        print("   1. 检查并修改 !important 规则，改为使用主题变量")
    if results["specific_selectors"]:
        print("   2. 检查高特异性选择器，确保不会覆盖全局样式")
    if results["css_files"]:
        print("   3. 统一CSS文件中的表头样式，使用主题变量")

if __name__ == "__main__":
    main()
