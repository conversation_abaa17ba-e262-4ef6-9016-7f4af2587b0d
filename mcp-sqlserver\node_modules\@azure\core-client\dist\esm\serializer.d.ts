import type { Serializer } from "./interfaces.js";
/**
 * Method that creates and returns a Serializer.
 * @param modelMappers - Known models to map
 * @param isXML - If XML should be supported
 */
export declare function createSerializer(modelMappers?: {
    [key: string]: any;
}, isXML?: boolean): Serializer;
/**
 * Known types of Mappers
 */
export declare const MapperTypeNames: {
    readonly Base64Url: "Base64Url";
    readonly Boolean: "Boolean";
    readonly ByteArray: "ByteArray";
    readonly Composite: "Composite";
    readonly Date: "Date";
    readonly DateTime: "DateTime";
    readonly DateTimeRfc1123: "DateTimeRfc1123";
    readonly Dictionary: "Dictionary";
    readonly Enum: "Enum";
    readonly Number: "Number";
    readonly Object: "Object";
    readonly Sequence: "Sequence";
    readonly String: "String";
    readonly Stream: "Stream";
    readonly TimeSpan: "TimeSpan";
    readonly UnixTime: "UnixTime";
};
//# sourceMappingURL=serializer.d.ts.map