{"version": 3, "file": "tracingPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/tracingPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AA6ClC,sCA0CC;AArFD,sDAK6B;AAC7B,kDAA8C;AAG9C,uDAAyD;AACzD,sCAAmC;AACnC,gDAA4D;AAC5D,kDAA8C;AAC9C,kEAAoE;AAEpE;;GAEG;AACU,QAAA,iBAAiB,GAAG,eAAe,CAAC;AAmBjD;;;;;GAKG;AACH,SAAgB,aAAa,CAAC,UAAgC,EAAE;IAC9D,MAAM,gBAAgB,GAAG,IAAA,gCAAiB,EAAC,OAAO,CAAC,eAAe,CAAC,CAAC;IACpE,MAAM,SAAS,GAAG,IAAI,gBAAS,CAAC;QAC9B,gCAAgC,EAAE,OAAO,CAAC,gCAAgC;KAC3E,CAAC,CAAC;IACH,MAAM,aAAa,GAAG,sBAAsB,EAAE,CAAC;IAE/C,OAAO;QACL,IAAI,EAAE,yBAAiB;QACvB,KAAK,CAAC,WAAW,CAAC,OAAwB,EAAE,IAAiB;;YAC3D,IAAI,CAAC,aAAa,EAAE,CAAC;gBACnB,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC;YAEzC,MAAM,cAAc,GAAG;gBACrB,UAAU,EAAE,SAAS,CAAC,WAAW,CAAC,OAAO,CAAC,GAAG,CAAC;gBAC9C,aAAa,EAAE,OAAO,CAAC,MAAM;gBAC7B,iBAAiB,EAAE,SAAS;gBAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;aAC7B,CAAC;YACF,IAAI,SAAS,EAAE,CAAC;gBACd,cAAc,CAAC,iBAAiB,CAAC,GAAG,SAAS,CAAC;YAChD,CAAC;YAED,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,MAAA,aAAa,CAAC,aAAa,EAAE,OAAO,EAAE,cAAc,CAAC,mCAAI,EAAE,CAAC;YAE7F,IAAI,CAAC,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBAC7B,OAAO,IAAI,CAAC,OAAO,CAAC,CAAC;YACvB,CAAC;YAED,IAAI,CAAC;gBACH,MAAM,QAAQ,GAAG,MAAM,aAAa,CAAC,WAAW,CAAC,cAAc,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;gBAChF,kBAAkB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;gBACnC,OAAO,QAAQ,CAAC;YAClB,CAAC;YAAC,OAAO,GAAQ,EAAE,CAAC;gBAClB,eAAe,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;gBAC3B,MAAM,GAAG,CAAC;YACZ,CAAC;QACH,CAAC;KACF,CAAC;AACJ,CAAC;AAED,SAAS,sBAAsB;IAC7B,IAAI,CAAC;QACH,OAAO,IAAA,kCAAmB,EAAC;YACzB,SAAS,EAAE,EAAE;YACb,WAAW,EAAE,2BAA2B;YACxC,cAAc,EAAE,0BAAW;SAC5B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,CAAU,EAAE,CAAC;QACpB,eAAM,CAAC,OAAO,CAAC,0CAA0C,IAAA,2BAAe,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC/E,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,aAAa,CACpB,aAA4B,EAC5B,OAAwB,EACxB,cAAuC;IAEvC,IAAI,CAAC;QACH,oFAAoF;QACpF,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,GAAG,aAAa,CAAC,SAAS,CACtD,QAAQ,OAAO,CAAC,MAAM,EAAE,EACxB,EAAE,cAAc,EAAE,OAAO,CAAC,cAAc,EAAE,EAC1C;YACE,QAAQ,EAAE,QAAQ;YAClB,cAAc;SACf,CACF,CAAC;QAEF,wDAAwD;QACxD,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,EAAE,CAAC;YACX,OAAO,SAAS,CAAC;QACnB,CAAC;QAED,cAAc;QACd,MAAM,OAAO,GAAG,aAAa,CAAC,oBAAoB,CAChD,cAAc,CAAC,cAAc,CAAC,cAAc,CAC7C,CAAC;QACF,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;YACnD,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;QAClC,CAAC;QACD,OAAO,EAAE,IAAI,EAAE,cAAc,EAAE,cAAc,CAAC,cAAc,CAAC,cAAc,EAAE,CAAC;IAChF,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,eAAM,CAAC,OAAO,CAAC,qDAAqD,IAAA,2BAAe,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1F,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,SAAS,eAAe,CAAC,IAAiB,EAAE,KAAc;IACxD,IAAI,CAAC;QACH,IAAI,CAAC,SAAS,CAAC;YACb,MAAM,EAAE,OAAO;YACf,KAAK,EAAE,IAAA,mBAAO,EAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SAC1C,CAAC,CAAC;QACH,IAAI,IAAA,0BAAW,EAAC,KAAK,CAAC,IAAI,KAAK,CAAC,UAAU,EAAE,CAAC;YAC3C,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;QAC1D,CAAC;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,eAAM,CAAC,OAAO,CAAC,qDAAqD,IAAA,2BAAe,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5F,CAAC;AACH,CAAC;AAED,SAAS,kBAAkB,CAAC,IAAiB,EAAE,QAA0B;IACvE,IAAI,CAAC;QACH,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,QAAQ,CAAC,MAAM,CAAC,CAAC;QACvD,MAAM,gBAAgB,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,CAAC;QACjE,IAAI,gBAAgB,EAAE,CAAC;YACrB,IAAI,CAAC,YAAY,CAAC,kBAAkB,EAAE,gBAAgB,CAAC,CAAC;QAC1D,CAAC;QACD,2FAA2F;QAC3F,2CAA2C;QAC3C,sEAAsE;QACtE,IAAI,QAAQ,CAAC,MAAM,IAAI,GAAG,EAAE,CAAC;YAC3B,IAAI,CAAC,SAAS,CAAC;gBACb,MAAM,EAAE,OAAO;aAChB,CAAC,CAAC;QACL,CAAC;QACD,IAAI,CAAC,GAAG,EAAE,CAAC;IACb,CAAC;IAAC,OAAO,CAAM,EAAE,CAAC;QAChB,eAAM,CAAC,OAAO,CAAC,qDAAqD,IAAA,2BAAe,EAAC,CAAC,CAAC,EAAE,CAAC,CAAC;IAC5F,CAAC;AACH,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport {\n  type TracingClient,\n  type TracingContext,\n  type TracingSpan,\n  createTracingClient,\n} from \"@azure/core-tracing\";\nimport { SDK_VERSION } from \"../constants.js\";\nimport type { PipelineRequest, PipelineResponse, SendRequest } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\nimport { getUserAgentValue } from \"../util/userAgent.js\";\nimport { logger } from \"../log.js\";\nimport { getErrorMessage, isError } from \"@azure/core-util\";\nimport { isRestError } from \"../restError.js\";\nimport { Sanitizer } from \"@typespec/ts-http-runtime/internal/util\";\n\n/**\n * The programmatic identifier of the tracingPolicy.\n */\nexport const tracingPolicyName = \"tracingPolicy\";\n\n/**\n * Options to configure the tracing policy.\n */\nexport interface TracingPolicyOptions {\n  /**\n   * String prefix to add to the user agent logged as metadata\n   * on the generated Span.\n   * Defaults to an empty string.\n   */\n  userAgentPrefix?: string;\n  /**\n   * Query string names whose values will be logged when logging is enabled. By default no\n   * query string values are logged.\n   */\n  additionalAllowedQueryParameters?: string[];\n}\n\n/**\n * A simple policy to create OpenTelemetry Spans for each request made by the pipeline\n * that has SpanOptions with a parent.\n * Requests made without a parent Span will not be recorded.\n * @param options - Options to configure the telemetry logged by the tracing policy.\n */\nexport function tracingPolicy(options: TracingPolicyOptions = {}): PipelinePolicy {\n  const userAgentPromise = getUserAgentValue(options.userAgentPrefix);\n  const sanitizer = new Sanitizer({\n    additionalAllowedQueryParameters: options.additionalAllowedQueryParameters,\n  });\n  const tracingClient = tryCreateTracingClient();\n\n  return {\n    name: tracingPolicyName,\n    async sendRequest(request: PipelineRequest, next: SendRequest): Promise<PipelineResponse> {\n      if (!tracingClient) {\n        return next(request);\n      }\n\n      const userAgent = await userAgentPromise;\n\n      const spanAttributes = {\n        \"http.url\": sanitizer.sanitizeUrl(request.url),\n        \"http.method\": request.method,\n        \"http.user_agent\": userAgent,\n        requestId: request.requestId,\n      };\n      if (userAgent) {\n        spanAttributes[\"http.user_agent\"] = userAgent;\n      }\n\n      const { span, tracingContext } = tryCreateSpan(tracingClient, request, spanAttributes) ?? {};\n\n      if (!span || !tracingContext) {\n        return next(request);\n      }\n\n      try {\n        const response = await tracingClient.withContext(tracingContext, next, request);\n        tryProcessResponse(span, response);\n        return response;\n      } catch (err: any) {\n        tryProcessError(span, err);\n        throw err;\n      }\n    },\n  };\n}\n\nfunction tryCreateTracingClient(): TracingClient | undefined {\n  try {\n    return createTracingClient({\n      namespace: \"\",\n      packageName: \"@azure/core-rest-pipeline\",\n      packageVersion: SDK_VERSION,\n    });\n  } catch (e: unknown) {\n    logger.warning(`Error when creating the TracingClient: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryCreateSpan(\n  tracingClient: TracingClient,\n  request: PipelineRequest,\n  spanAttributes: Record<string, unknown>,\n): { span: TracingSpan; tracingContext: TracingContext } | undefined {\n  try {\n    // As per spec, we do not need to differentiate between HTTP and HTTPS in span name.\n    const { span, updatedOptions } = tracingClient.startSpan(\n      `HTTP ${request.method}`,\n      { tracingOptions: request.tracingOptions },\n      {\n        spanKind: \"client\",\n        spanAttributes,\n      },\n    );\n\n    // If the span is not recording, don't do any more work.\n    if (!span.isRecording()) {\n      span.end();\n      return undefined;\n    }\n\n    // set headers\n    const headers = tracingClient.createRequestHeaders(\n      updatedOptions.tracingOptions.tracingContext,\n    );\n    for (const [key, value] of Object.entries(headers)) {\n      request.headers.set(key, value);\n    }\n    return { span, tracingContext: updatedOptions.tracingOptions.tracingContext };\n  } catch (e: any) {\n    logger.warning(`Skipping creating a tracing span due to an error: ${getErrorMessage(e)}`);\n    return undefined;\n  }\n}\n\nfunction tryProcessError(span: TracingSpan, error: unknown): void {\n  try {\n    span.setStatus({\n      status: \"error\",\n      error: isError(error) ? error : undefined,\n    });\n    if (isRestError(error) && error.statusCode) {\n      span.setAttribute(\"http.status_code\", error.statusCode);\n    }\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n\nfunction tryProcessResponse(span: TracingSpan, response: PipelineResponse): void {\n  try {\n    span.setAttribute(\"http.status_code\", response.status);\n    const serviceRequestId = response.headers.get(\"x-ms-request-id\");\n    if (serviceRequestId) {\n      span.setAttribute(\"serviceRequestId\", serviceRequestId);\n    }\n    // Per semantic conventions, only set the status to error if the status code is 4xx or 5xx.\n    // Otherwise, the status MUST remain unset.\n    // https://opentelemetry.io/docs/specs/semconv/http/http-spans/#status\n    if (response.status >= 400) {\n      span.setStatus({\n        status: \"error\",\n      });\n    }\n    span.end();\n  } catch (e: any) {\n    logger.warning(`Skipping tracing span processing due to an error: ${getErrorMessage(e)}`);\n  }\n}\n"]}