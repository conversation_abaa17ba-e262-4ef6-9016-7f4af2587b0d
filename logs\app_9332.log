2025-06-22 18:23:24,411 INFO: 应用启动 - PID: 9332 [in C:\StudentsCMSSP\app\__init__.py:862]
2025-06-22 18:23:51,116 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-22 18:23:51,117 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-22 18:23:51,118 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-22 18:23:51,118 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-22 18:24:11,438 INFO: 库存查询条件: 状态=正常, 数量>0 [in C:\StudentsCMSSP\app\routes\inventory.py:151]
2025-06-22 18:27:56,844 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('supplier_product_list', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:30:31,963 INFO: 当前用户: guest_demo [in C:\StudentsCMSSP\app\routes\consumption_plan.py:94]
2025-06-22 18:30:31,964 INFO: 用户区域ID: 44 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:95]
2025-06-22 18:30:31,965 INFO: 用户区域名称: 海淀区中关村第一小学 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:96]
2025-06-22 18:30:31,966 INFO: 是否管理员: 0 [in C:\StudentsCMSSP\app\routes\consumption_plan.py:97]
2025-06-22 18:31:03,066 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('inspection_records', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:31:39,071 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('purchase_order_create', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
2025-06-22 18:31:39,078 ERROR: 获取模块可见性时出错: (pyodbc.Error) ('HY000', '[HY000] [Microsoft][ODBC SQL Server Driver]连接占线导致另一个 hstmt (0) (SQLExecDirectW)')
[SQL: 
                SELECT TOP 1 is_visible
                FROM module_visibility
                WHERE module_id = ? AND role_id = ?
            ]
[parameters: ('ingredient_management', 5)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\StudentsCMSSP\app\models_visibility.py:54]
