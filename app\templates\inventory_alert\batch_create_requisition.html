{% extends 'base.html' %}

{% block title %}批量创建采购申请{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">批量创建采购申请</h3>
                    <div class="card-tools">
                        <a href="{{ url_for('inventory_alert.index') }}" class="btn btn-default btn-sm">
                            <i class="fas fa-arrow-left"></i> 返回列表
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <form method="post" action="{{ url_for('inventory_alert.batch_create_requisition') }}"><div class="form-group">
                            <label for="required_date">需求日期 <span class="text-danger">*</span></label>
                            <input type="date" name="required_date" id="required_date" class="form-control" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="notes">备注</label>
                            <textarea name="notes" id="notes" class="form-control" rows="3"></textarea>
                        </div>
                        
                        {% for area_id, alerts in alerts_by_area.items() %}
                        <div class="card mt-4">
                            <div class="card-header">
                                <h4 class="card-title">{{ areas[area_id].name }}</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-bordered table-striped">
                                        <thead>
                                            <tr>
                                                <th style="width: 5%;">选择</th>
                                                <th style="width: 5%;">ID</th>
                                                <th class="w-25">食材</th>
                                                <th class="w-15">当前库存</th>
                                                <th class="w-15">预警类型</th>
                                                <th class="w-20">采购数量</th>
                                                <th class="w-15">备注</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for alert in alerts %}
                                            <tr>
                                                <td>
                                                    <div class="icheck-primary">
                                                        <input type="checkbox" name="alert_ids[]" id="alert_{{ alert.id }}" value="{{ alert.id }}" checked>
                                                        <label for="alert_{{ alert.id }}"></label>
                                                    </div>
                                                </td>
                                                <td>{{ alert.id }}</td>
                                                <td>{{ alert.ingredient.name }}</td>
                                                <td>{{ alert.current_quantity }} {{ alert.unit }}</td>
                                                <td>
                                                    {% if alert.alert_type == '库存不足' %}
                                                    <span class="badge badge-danger">库存不足</span>
                                                    {% elif alert.alert_type == '库存过多' %}
                                                    <span class="badge badge-warning">库存过多</span>
                                                    {% elif alert.alert_type == '临近过期' %}
                                                    <span class="badge badge-info">临近过期</span>
                                                    {% endif %}
                                                </td>
                                                <td>
                                                    <div class="input-group">
                                                        <input type="number" name="quantity_{{ alert.id }}" class="form-control" min="0.1" step="0.1" value="{{ alert.current_quantity * 2 }}" required>
                                                        <div class="input-group-append">
                                                            <span class="input-group-text">{{ alert.unit }}</span>
                                                        </div>
                                                    </div>
                                                </td>
                                                <td>{{ alert.notes or '-' }}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        {% endfor %}
                        
                        <div class="form-group text-center mt-4">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save"></i> 创建采购申请
                            </button>
                            <a href="{{ url_for('inventory_alert.index') }}" class="btn btn-default">
                                <i class="fas fa-times"></i> 取消
                            </a>
                        </div>
                    
    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"></form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
{{ super() }}
<script nonce="{{ csp_nonce }}">
    $(document).ready(function() {
        // 设置默认需求日期为明天
        var tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        var tomorrowStr = tomorrow.toISOString().split('T')[0];
        $('#required_date').val(tomorrowStr);
    });
</script>
{% endblock %}
