{"version": 3, "file": "proxyPolicy.js", "sourceRoot": "", "sources": ["../../../src/policies/proxyPolicy.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAuBlC,0DAEC;AASD,kCAQC;AArCD,0EAIqD;AAErD;;GAEG;AACU,QAAA,eAAe,GAAG,0BAAkB,CAAC;AAElD;;;;;;GAMG;AACH,SAAgB,uBAAuB,CAAC,QAAiB;IACvD,OAAO,IAAA,kCAA0B,EAAC,QAAQ,CAAC,CAAC;AAC9C,CAAC;AAED;;;;;;GAMG;AACH,SAAgB,WAAW,CACzB,aAA6B,EAC7B,OAGC;IAED,OAAO,IAAA,sBAAc,EAAC,aAAa,EAAE,OAAO,CAAC,CAAC;AAChD,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { ProxySettings } from \"../interfaces.js\";\nimport type { PipelinePolicy } from \"../pipeline.js\";\n\nimport {\n  proxyPolicy as tspProxyPolicy,\n  proxyPolicyName as tspProxyPolicyName,\n  getDefaultProxySettings as tspGetDefaultProxySettings,\n} from \"@typespec/ts-http-runtime/internal/policies\";\n\n/**\n * The programmatic identifier of the proxyPolicy.\n */\nexport const proxyPolicyName = tspProxyPolicyName;\n\n/**\n * This method converts a proxy url into `ProxySettings` for use with ProxyPolicy.\n * If no argument is given, it attempts to parse a proxy URL from the environment\n * variables `HTTPS_PROXY` or `HTTP_PROXY`.\n * @param proxyUrl - The url of the proxy to use. May contain authentication information.\n * @deprecated - Internally this method is no longer necessary when setting proxy information.\n */\nexport function getDefaultProxySettings(proxyUrl?: string): ProxySettings | undefined {\n  return tspGetDefaultProxySettings(proxyUrl);\n}\n\n/**\n * A policy that allows one to apply proxy settings to all requests.\n * If not passed static settings, they will be retrieved from the HTTPS_PROXY\n * or HTTP_PROXY environment variables.\n * @param proxySettings - ProxySettings to use on each request.\n * @param options - additional settings, for example, custom NO_PROXY patterns\n */\nexport function proxyPolicy(\n  proxySettings?: ProxySettings,\n  options?: {\n    /** a list of patterns to override those loaded from NO_PROXY environment variable. */\n    customNoProxyList?: string[];\n  },\n): PipelinePolicy {\n  return tspProxyPolicy(proxySettings, options);\n}\n"]}