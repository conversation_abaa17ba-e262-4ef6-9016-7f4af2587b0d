<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>表头主题色测试页面</title>
    
    <!-- Bootstrap CSS -->
    <link href="app/static/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- 主题颜色CSS -->
    <link href="app/static/css/theme-colors.css" rel="stylesheet">
    
    <style>
        body {
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .test-title {
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="text-center mb-5">表头主题色强制应用测试</h1>
        
        <!-- 测试1: 基础表格 -->
        <div class="test-section">
            <h3 class="test-title">测试1: 基础表格</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th>订单号</th>
                        <th>创建时间</th>
                        <th>总金额</th>
                        <th>状态</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>PO-2024-001</td>
                        <td>2024-06-22 10:30</td>
                        <td>¥1,250.00</td>
                        <td><span class="badge badge-success">已完成</span></td>
                        <td><button class="btn btn-sm btn-primary">查看</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试2: thead-light 样式 -->
        <div class="test-section">
            <h3 class="test-title">测试2: thead-light 样式（应该被覆盖）</h3>
            <table class="table">
                <thead class="thead-light">
                    <tr>
                        <th>商品名称</th>
                        <th>规格</th>
                        <th>单价</th>
                        <th>数量</th>
                        <th>小计</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>大米</td>
                        <td>25kg/袋</td>
                        <td>¥85.00</td>
                        <td>10</td>
                        <td>¥850.00</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试3: thead-dark 样式 -->
        <div class="test-section">
            <h3 class="test-title">测试3: thead-dark 样式（应该被覆盖）</h3>
            <table class="table">
                <thead class="thead-dark">
                    <tr>
                        <th>学生姓名</th>
                        <th>班级</th>
                        <th>用餐时间</th>
                        <th>菜品</th>
                        <th>费用</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>张三</td>
                        <td>三年级一班</td>
                        <td>12:00</td>
                        <td>红烧肉套餐</td>
                        <td>¥15.00</td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试4: table-striped 样式 -->
        <div class="test-section">
            <h3 class="test-title">测试4: table-striped 样式</h3>
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>供应商</th>
                        <th>联系人</th>
                        <th>电话</th>
                        <th>地址</th>
                        <th>状态</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>绿色农场</td>
                        <td>李经理</td>
                        <td>138-0000-0001</td>
                        <td>北京市朝阳区</td>
                        <td><span class="badge badge-success">合作中</span></td>
                    </tr>
                    <tr>
                        <td>新鲜蔬菜</td>
                        <td>王总</td>
                        <td>139-0000-0002</td>
                        <td>上海市浦东新区</td>
                        <td><span class="badge badge-warning">待审核</span></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试5: table-hover 样式 -->
        <div class="test-section">
            <h3 class="test-title">测试5: table-hover 样式</h3>
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>菜品名称</th>
                        <th>分类</th>
                        <th>价格</th>
                        <th>库存</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>宫保鸡丁</td>
                        <td>热菜</td>
                        <td>¥18.00</td>
                        <td>充足</td>
                        <td><button class="btn btn-sm btn-outline-primary">编辑</button></td>
                    </tr>
                    <tr>
                        <td>西红柿鸡蛋</td>
                        <td>热菜</td>
                        <td>¥12.00</td>
                        <td>充足</td>
                        <td><button class="btn btn-sm btn-outline-primary">编辑</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试6: 内联样式表头（应该被覆盖） -->
        <div class="test-section">
            <h3 class="test-title">测试6: 内联样式表头（应该被覆盖）</h3>
            <table class="table">
                <thead>
                    <tr>
                        <th style="background-color: #f8f9fa; color: #333;">财务科目</th>
                        <th style="background-color: gray; color: white;">借方金额</th>
                        <th style="background-color: lightgray;">贷方金额</th>
                        <th style="background-color: #e9ecef;">余额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>现金</td>
                        <td>¥10,000.00</td>
                        <td>¥0.00</td>
                        <td>¥10,000.00</td>
                        <td><button class="btn btn-sm btn-info">详情</button></td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <!-- 测试7: 响应式表格 -->
        <div class="test-section">
            <h3 class="test-title">测试7: 响应式表格</h3>
            <div class="table-responsive">
                <table class="table">
                    <thead>
                        <tr>
                            <th>库存编号</th>
                            <th>商品名称</th>
                            <th>入库时间</th>
                            <th>入库数量</th>
                            <th>剩余数量</th>
                            <th>单位</th>
                            <th>供应商</th>
                            <th>状态</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>INV-2024-001</td>
                            <td>优质大米</td>
                            <td>2024-06-20</td>
                            <td>100</td>
                            <td>85</td>
                            <td>袋</td>
                            <td>绿色农场</td>
                            <td><span class="badge badge-success">正常</span></td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        
        <!-- 测试8: 主题切换测试 -->
        <div class="test-section">
            <h3 class="test-title">测试8: 主题切换测试</h3>
            <div class="mb-3">
                <button class="btn btn-primary" onclick="setTheme('primary')">海洋蓝主题</button>
                <button class="btn btn-success" onclick="setTheme('success')">自然绿主题</button>
                <button class="btn btn-warning" onclick="setTheme('warning')">活力橙主题</button>
                <button class="btn btn-info" onclick="setTheme('info')">优雅紫主题</button>
                <button class="btn btn-danger" onclick="setTheme('danger')">深邃红主题</button>
                <button class="btn btn-secondary" onclick="setTheme('secondary')">现代灰主题</button>
            </div>
            
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>主题测试</th>
                        <th>表头颜色</th>
                        <th>应该跟随主题</th>
                        <th>实时变化</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>当前主题</td>
                        <td id="current-theme">海洋蓝</td>
                        <td>是</td>
                        <td>✓</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>
    
    <script>
        function setTheme(theme) {
            document.documentElement.setAttribute('data-theme', theme);
            
            const themeNames = {
                'primary': '海洋蓝',
                'success': '自然绿', 
                'warning': '活力橙',
                'info': '优雅紫',
                'danger': '深邃红',
                'secondary': '现代灰'
            };
            
            document.getElementById('current-theme').textContent = themeNames[theme] || '海洋蓝';
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            console.log('表头主题色测试页面已加载');
            console.log('所有表头应该显示为主题色背景，白色文字');
            console.log('可以通过主题切换按钮测试不同主题下的表头颜色');
        });
    </script>
</body>
</html>
