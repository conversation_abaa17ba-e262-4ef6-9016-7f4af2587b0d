#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找项目中所有带有H3标题和按钮的页面头部布局
识别需要优化的页面表头
"""

import os
import re
from pathlib import Path

def find_h3_with_buttons():
    """查找所有带有H3和按钮的页面"""
    
    templates_dir = Path("app/templates")
    results = {
        "h3_with_buttons": [],      # H3标题和按钮在同一区域
        "h3_without_flex": [],      # H3标题但没有使用flex布局
        "good_layouts": [],         # 已经使用了好的布局
        "summary": {
            "total_files": 0,
            "files_with_h3": 0,
            "needs_optimization": 0,
            "good_layouts": 0
        }
    }
    
    # 匹配模式
    patterns = {
        # H3标题
        "h3_tags": re.compile(r'<h3[^>]*>(.*?)</h3>', re.IGNORECASE | re.DOTALL),
        # 按钮
        "btn_tags": re.compile(r'<[^>]*class="[^"]*btn[^"]*"[^>]*>', re.IGNORECASE),
        "btn_links": re.compile(r'<a[^>]*class="[^"]*btn[^"]*"[^>]*>', re.IGNORECASE),
        # 好的布局模式
        "good_flex": re.compile(r'd-flex\s+justify-content-between\s+align-items-center', re.IGNORECASE),
        "card_tools": re.compile(r'card-tools', re.IGNORECASE),
        # 卡片头部
        "card_header": re.compile(r'<div[^>]*class="[^"]*card-header[^"]*"[^>]*>(.*?)</div>', re.IGNORECASE | re.DOTALL),
    }
    
    def analyze_file(file_path):
        """分析单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        relative_path = str(file_path.relative_to(templates_dir))
        has_h3 = False
        
        # 查找H3标题
        h3_matches = patterns["h3_tags"].findall(content)
        if not h3_matches:
            return False
        
        has_h3 = True
        results["summary"]["files_with_h3"] += 1
        
        # 查找按钮
        btn_matches = patterns["btn_tags"].findall(content)
        btn_link_matches = patterns["btn_links"].findall(content)
        
        if not btn_matches and not btn_link_matches:
            return True  # 有H3但没有按钮，不需要优化
        
        # 检查是否已经使用了好的布局
        has_good_flex = patterns["good_flex"].search(content)
        has_card_tools = patterns["card_tools"].search(content)
        
        if has_good_flex or has_card_tools:
            results["good_layouts"].append({
                "file": relative_path,
                "h3_count": len(h3_matches),
                "btn_count": len(btn_matches) + len(btn_link_matches),
                "layout_type": "flex" if has_good_flex else "card-tools"
            })
            results["summary"]["good_layouts"] += 1
        else:
            # 需要优化的页面
            # 查找卡片头部内容
            card_header_matches = patterns["card_header"].findall(content)
            
            results["h3_without_flex"].append({
                "file": relative_path,
                "h3_titles": [h3.strip() for h3 in h3_matches],
                "btn_count": len(btn_matches) + len(btn_link_matches),
                "has_card_header": len(card_header_matches) > 0,
                "card_header_content": card_header_matches[0][:200] + "..." if card_header_matches else None
            })
            results["summary"]["needs_optimization"] += 1
        
        return True
    
    # 遍历所有HTML文件
    for file_path in templates_dir.rglob("*.html"):
        results["summary"]["total_files"] += 1
        analyze_file(file_path)
    
    return results

def print_results(results):
    """打印分析结果"""
    print("🔍 H3标题和按钮布局分析报告")
    print("=" * 50)
    
    summary = results["summary"]
    print(f"📊 总体统计:")
    print(f"   总文件数: {summary['total_files']}")
    print(f"   包含H3的文件: {summary['files_with_h3']}")
    print(f"   需要优化的文件: {summary['needs_optimization']}")
    print(f"   已优化的文件: {summary['good_layouts']}")
    print()
    
    # 需要优化的页面
    if results["h3_without_flex"]:
        print(f"🚨 需要优化的页面 ({len(results['h3_without_flex'])} 个):")
        for item in results["h3_without_flex"]:
            print(f"   📁 {item['file']}")
            print(f"      H3标题: {', '.join(item['h3_titles'][:2])}{'...' if len(item['h3_titles']) > 2 else ''}")
            print(f"      按钮数量: {item['btn_count']}")
            print(f"      有卡片头部: {'是' if item['has_card_header'] else '否'}")
            if item['card_header_content']:
                print(f"      头部内容: {item['card_header_content']}")
            print()
    
    # 已经优化的页面
    if results["good_layouts"]:
        print(f"✅ 已优化的页面 ({len(results['good_layouts'])} 个):")
        for item in results["good_layouts"]:
            print(f"   📁 {item['file']}")
            print(f"      H3数量: {item['h3_count']}, 按钮数量: {item['btn_count']}")
            print(f"      布局类型: {item['layout_type']}")
            print()

if __name__ == "__main__":
    results = find_h3_with_buttons()
    print_results(results)
