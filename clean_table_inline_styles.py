#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理表头内联样式工具
删除所有模板文件中表头的内联样式，让CSS文件统一控制
"""

import os
import re
from pathlib import Path

def clean_table_inline_styles():
    """清理所有模板中表头的内联样式"""
    
    templates_dir = Path("app/templates")
    results = {
        "processed_files": [],
        "changes": [],
        "summary": {
            "total_files": 0,
            "modified_files": 0,
            "removed_styles": 0
        }
    }
    
    # 匹配表头内联样式的模式
    patterns = {
        # 匹配 <th style="..."> 
        "th_with_style": re.compile(r'<th([^>]*)\s+style="([^"]*)"([^>]*)>', re.IGNORECASE),
        # 匹配背景色相关样式
        "background_styles": re.compile(r'background[^;]*;?', re.IGNORECASE),
        "width_styles": re.compile(r'width\s*:\s*[^;]+;?', re.IGNORECASE),
    }
    
    def clean_file(file_path):
        """清理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False, content
        
        original_content = content
        relative_path = str(file_path.relative_to(templates_dir))
        file_changes = []
        
        def replace_th_style(match):
            """替换表头样式的回调函数"""
            before_attrs = match.group(1) if match.group(1) else ""
            style_content = match.group(2)
            after_attrs = match.group(3) if match.group(3) else ""
            
            # 保留宽度样式，删除背景色样式
            remaining_styles = []
            
            # 分割样式属性
            style_parts = [s.strip() for s in style_content.split(';') if s.strip()]
            
            for style_part in style_parts:
                # 保留宽度样式
                if patterns["width_styles"].match(style_part):
                    remaining_styles.append(style_part)
                # 删除背景色样式
                elif patterns["background_styles"].match(style_part):
                    file_changes.append(f"删除背景样式: {style_part}")
                    results["summary"]["removed_styles"] += 1
                # 保留其他非背景色样式
                elif not any(bg_word in style_part.lower() for bg_word in ['background', 'color']):
                    remaining_styles.append(style_part)
                else:
                    file_changes.append(f"删除颜色样式: {style_part}")
                    results["summary"]["removed_styles"] += 1
            
            # 重新构建标签
            if remaining_styles:
                new_style = '; '.join(remaining_styles)
                if not new_style.endswith(';'):
                    new_style += ';'
                return f'<th{before_attrs} style="{new_style}"{after_attrs}>'
            else:
                # 如果没有剩余样式，删除整个style属性
                return f'<th{before_attrs}{after_attrs}>'
        
        # 替换所有表头内联样式
        new_content = patterns["th_with_style"].sub(replace_th_style, content)
        
        # 检查是否有修改
        if new_content != original_content:
            # 写回文件
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                results["changes"].append({
                    "file": relative_path,
                    "changes": file_changes
                })
                
                return True, new_content
            except Exception as e:
                print(f"写入文件失败 {file_path}: {e}")
                return False, original_content
        
        return False, content
    
    # 遍历所有HTML文件
    for file_path in templates_dir.rglob("*.html"):
        results["summary"]["total_files"] += 1
        
        modified, content = clean_file(file_path)
        
        if modified:
            results["summary"]["modified_files"] += 1
            results["processed_files"].append(str(file_path.relative_to(templates_dir)))
    
    return results

def generate_report(results):
    """生成清理报告"""
    
    print("=" * 80)
    print("表头内联样式清理报告")
    print("=" * 80)
    
    # 统计信息
    summary = results["summary"]
    print(f"\n📊 统计信息:")
    print(f"   总文件数: {summary['total_files']}")
    print(f"   修改文件数: {summary['modified_files']}")
    print(f"   删除样式数: {summary['removed_styles']}")
    
    # 修改详情
    if results["changes"]:
        print(f"\n🔧 修改详情:")
        for change in results["changes"]:
            print(f"\n📁 {change['file']}:")
            for detail in change["changes"]:
                print(f"   - {detail}")
    
    # 处理的文件列表
    if results["processed_files"]:
        print(f"\n📝 已处理的文件:")
        for file_path in results["processed_files"]:
            print(f"   ✅ {file_path}")

def main():
    """主函数"""
    print("🧹 开始清理表头内联样式...")
    
    # 确认操作
    response = input("\n⚠️  这将修改模板文件，是否继续？(y/N): ")
    if response.lower() != 'y':
        print("❌ 操作已取消")
        return
    
    results = clean_table_inline_styles()
    generate_report(results)
    
    print("\n✅ 清理完成！")
    
    # 提醒
    print("\n💡 提醒:")
    print("   1. 表头样式现在由CSS文件统一控制")
    print("   2. 请检查页面显示是否正常")
    print("   3. 如有问题可通过Git回滚")

if __name__ == "__main__":
    main()
