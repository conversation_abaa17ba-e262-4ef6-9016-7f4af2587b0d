#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
灰色表头验证工具
验证表头是否已强制设置为灰色
"""

import re
from pathlib import Path
from datetime import datetime

def verify_gray_header_settings():
    """验证灰色表头设置"""
    
    theme_file = Path("app/static/css/theme-colors.css")
    
    if not theme_file.exists():
        return False, "主题颜色文件不存在"
    
    try:
        with open(theme_file, 'r', encoding='utf-8') as f:
            content = f.read()
    except Exception as e:
        return False, f"读取主题文件失败: {e}"
    
    # 检查表头颜色变量是否设置为灰色
    checks = {
        "背景色": {
            "pattern": r'--theme-table-header-bg:\s*#f8f9fa',
            "expected": "#f8f9fa (浅灰色)"
        },
        "文字颜色": {
            "pattern": r'--theme-table-header-color:\s*#495057',
            "expected": "#495057 (深灰色)"
        },
        "边框颜色": {
            "pattern": r'--theme-table-header-border:\s*#dee2e6',
            "expected": "#dee2e6 (中灰色)"
        },
        "悬停背景": {
            "pattern": r'background-color:\s*#e9ecef\s*!important',
            "expected": "#e9ecef (悬停灰色)"
        },
        "悬停文字": {
            "pattern": r'color:\s*#495057\s*!important',
            "expected": "#495057 (悬停文字灰色)"
        }
    }
    
    results = {}
    for check_name, check_info in checks.items():
        if re.search(check_info["pattern"], content):
            results[check_name] = {"status": "✅", "value": check_info["expected"]}
        else:
            results[check_name] = {"status": "❌", "value": "未找到或不正确"}
    
    # 检查是否还有主题色引用
    theme_references = [
        r'var\(--theme-primary\)',
        r'var\(--theme-primary-dark\)'
    ]
    
    remaining_theme_refs = []
    for ref_pattern in theme_references:
        matches = re.findall(ref_pattern, content)
        if matches:
            remaining_theme_refs.extend(matches)
    
    all_passed = all(result["status"] == "✅" for result in results.values())
    
    return all_passed, results, remaining_theme_refs

def generate_gray_header_report():
    """生成灰色表头验证报告"""
    
    success, results, theme_refs = verify_gray_header_settings()
    
    report_lines = []
    
    # 报告头部
    report_lines.extend([
        "# 灰色表头强制设置验证报告",
        "",
        f"**验证时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        "",
        "## 📊 验证结果",
        "",
    ])
    
    # 详细检查结果
    report_lines.append("### 🎨 表头颜色设置检查")
    report_lines.append("")
    
    for check_name, result in results.items():
        report_lines.append(f"- **{check_name}**: {result['status']} {result['value']}")
    
    report_lines.append("")
    
    # 主题引用检查
    if theme_refs:
        report_lines.extend([
            "### ⚠️ 发现的主题色引用",
            "",
            "以下位置仍在使用主题色变量（可能影响灰色设置）：",
            "",
        ])
        for ref in set(theme_refs):
            report_lines.append(f"- `{ref}`")
        report_lines.append("")
    else:
        report_lines.extend([
            "### ✅ 主题色引用检查",
            "",
            "未发现表头相关的主题色引用，灰色设置完全独立。",
            "",
        ])
    
    # 总体状态
    report_lines.extend([
        "## 🎯 总体验证状态",
        "",
    ])
    
    if success:
        report_lines.extend([
            "✅ **验证通过** - 表头已成功强制设置为灰色",
            "",
            "### 🎉 当前表头样式",
            "",
            "- **背景色**: `#f8f9fa` (浅灰色)",
            "- **文字颜色**: `#495057` (深灰色文字)",
            "- **边框颜色**: `#dee2e6` (中灰色边框)",
            "- **悬停背景**: `#e9ecef` (悬停时稍深的灰色)",
            "- **悬停文字**: `#495057` (悬停时保持深灰色文字)",
            "",
            "### 📋 特点",
            "",
            "- 不再跟随主题色变化",
            "- 统一的灰色调设计",
            "- 良好的对比度和可读性",
            "- 专业的企业级外观",
            "",
        ])
    else:
        report_lines.extend([
            "❌ **验证失败** - 表头灰色设置不完整",
            "",
            "### 🔧 需要修复的问题",
            "",
        ])
        
        for check_name, result in results.items():
            if result["status"] == "❌":
                report_lines.append(f"- 修复 {check_name} 设置")
        
        report_lines.append("")
    
    # 使用说明
    report_lines.extend([
        "## 📖 灰色表头说明",
        "",
        "### 🎨 颜色方案",
        "",
        "采用Bootstrap标准灰色调色板：",
        "",
        "```css",
        "/* 表头背景 */",
        "--theme-table-header-bg: #f8f9fa;      /* Bootstrap gray-100 */",
        "",
        "/* 表头文字 */", 
        "--theme-table-header-color: #495057;   /* Bootstrap gray-700 */",
        "",
        "/* 表头边框 */",
        "--theme-table-header-border: #dee2e6;  /* Bootstrap gray-300 */",
        "",
        "/* 悬停效果 */",
        "background-color: #e9ecef;              /* Bootstrap gray-200 */",
        "```",
        "",
        "### 🔄 如何恢复主题色",
        "",
        "如需恢复主题色表头，可以修改以下变量：",
        "",
        "```css",
        ":root {",
        "  --theme-table-header-bg: var(--theme-primary);",
        "  --theme-table-header-color: #ffffff;",
        "  --theme-table-header-border: var(--theme-primary-dark);",
        "}",
        "```",
        "",
        "### 🎯 自定义灰色",
        "",
        "如需使用其他灰色调，可以修改颜色值：",
        "",
        "```css",
        ":root {",
        "  /* 更深的灰色 */",
        "  --theme-table-header-bg: #e9ecef;",
        "  --theme-table-header-color: #343a40;",
        "  ",
        "  /* 更浅的灰色 */",
        "  --theme-table-header-bg: #ffffff;",
        "  --theme-table-header-color: #6c757d;",
        "}",
        "```",
        "",
    ])
    
    return "\n".join(report_lines)

def main():
    """主函数"""
    print("🔍 开始验证灰色表头设置...")
    
    # 验证灰色表头设置
    success, results, theme_refs = verify_gray_header_settings()
    
    print(f"📊 验证结果:")
    for check_name, result in results.items():
        print(f"   {result['status']} {check_name}: {result['value']}")
    
    if theme_refs:
        print(f"\n⚠️  发现 {len(set(theme_refs))} 个主题色引用")
    else:
        print(f"\n✅ 未发现主题色引用")
    
    print(f"\n🎯 总体状态: {'✅ 通过' if success else '❌ 失败'}")
    
    # 生成报告
    print("\n📝 生成验证报告...")
    report_content = generate_gray_header_report()
    
    report_file = "灰色表头验证报告.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print(f"📄 验证报告已生成: {report_file}")
    
    if success:
        print("\n🎉 恭喜！表头已成功强制设置为灰色")
        print("   - 背景色: #f8f9fa (浅灰色)")
        print("   - 文字色: #495057 (深灰色)")
        print("   - 边框色: #dee2e6 (中灰色)")
        print("   - 不再跟随主题色变化")
    else:
        print("\n⚠️  灰色设置不完整，请检查上述问题")

if __name__ == "__main__":
    main()
