{"version": 3, "file": "pipelineRequest.js", "sourceRoot": "", "sources": ["../../src/pipelineRequest.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAgBlC,OAAO,EACL,qBAAqB,IAAI,wBAAwB,GAElD,MAAM,2BAA2B,CAAC;AAgInC;;;;GAIG;AACH,MAAM,UAAU,qBAAqB,CAAC,OAA+B;IACnE,gHAAgH;IAChH,+IAA+I;IAC/I,wCAAwC;IACxC,OAAO,wBAAwB,CAAC,OAAoC,CAAC,CAAC;AACxE,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type {\n  Agent,\n  FormDataMap,\n  HttpHeaders,\n  MultipartRequestBody,\n  PipelineRequest,\n  ProxySettings,\n  RequestBodyType,\n  TlsSettings,\n  TransferProgressEvent,\n} from \"./interfaces.js\";\nimport type { AbortSignalLike } from \"@azure/abort-controller\";\nimport type { OperationTracingOptions } from \"@azure/core-tracing\";\nimport type { HttpMethods } from \"@azure/core-util\";\nimport {\n  createPipelineRequest as tspCreatePipelineRequest,\n  type PipelineRequestOptions as TspPipelineRequestOptions,\n} from \"@typespec/ts-http-runtime\";\n\n/**\n * Settings to initialize a request.\n * Almost equivalent to Partial<PipelineRequest>, but url is mandatory.\n */\nexport interface PipelineRequestOptions {\n  /**\n   * The URL to make the request to.\n   */\n  url: string;\n\n  /**\n   * The HTTP method to use when making the request.\n   */\n  method?: HttpMethods;\n\n  /**\n   * The HTTP headers to use when making the request.\n   */\n  headers?: HttpHeaders;\n\n  /**\n   * The number of milliseconds a request can take before automatically being terminated.\n   * If the request is terminated, an `AbortError` is thrown.\n   * Defaults to 0, which disables the timeout.\n   */\n  timeout?: number;\n\n  /**\n   * If credentials (cookies) should be sent along during an XHR.\n   * Defaults to false.\n   */\n  withCredentials?: boolean;\n\n  /**\n   * A unique identifier for the request. Used for logging and tracing.\n   */\n  requestId?: string;\n\n  /**\n   * The HTTP body content (if any)\n   */\n  body?: RequestBodyType;\n\n  /**\n   * Body for a multipart request.\n   */\n  multipartBody?: MultipartRequestBody;\n\n  /**\n   * To simulate a browser form post\n   */\n  formData?: FormDataMap;\n\n  /**\n   * A list of response status codes whose corresponding PipelineResponse body should be treated as a stream.\n   */\n  streamResponseStatusCodes?: Set<number>;\n\n  /**\n   * NODEJS ONLY\n   *\n   * A Node-only option to provide a custom `http.Agent`/`https.Agent`.\n   * NOTE: usually this should be one instance shared by multiple requests so that the underlying\n   *       connection to the service can be reused.\n   * Does nothing when running in the browser.\n   */\n  agent?: Agent;\n\n  /**\n   * BROWSER ONLY\n   *\n   * A browser only option to enable use of the Streams API. If this option is set and streaming is used\n   * (see `streamResponseStatusCodes`), the response will have a property `browserStream` instead of\n   * `blobBody` which will be undefined.\n   *\n   * Default value is false\n   */\n  enableBrowserStreams?: boolean;\n\n  /** Settings for configuring TLS authentication */\n  tlsSettings?: TlsSettings;\n\n  /**\n   * Proxy configuration.\n   */\n  proxySettings?: ProxySettings;\n\n  /**\n   * If the connection should not be reused.\n   */\n  disableKeepAlive?: boolean;\n\n  /**\n   * Used to abort the request later.\n   */\n  abortSignal?: AbortSignalLike;\n\n  /**\n   * Options used to create a span when tracing is enabled.\n   */\n  tracingOptions?: OperationTracingOptions;\n\n  /**\n   * Callback which fires upon upload progress.\n   */\n  onUploadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Callback which fires upon download progress. */\n  onDownloadProgress?: (progress: TransferProgressEvent) => void;\n\n  /** Set to true if the request is sent over HTTP instead of HTTPS */\n  allowInsecureConnection?: boolean;\n\n  /**\n   * Additional options to set on the request. This provides a way to override\n   * existing ones or provide request properties that are not declared.\n   *\n   * For possible valid properties, see\n   *   - NodeJS https.request options:  https://nodejs.org/api/http.html#httprequestoptions-callback\n   *   - Browser RequestInit: https://developer.mozilla.org/en-US/docs/Web/API/RequestInit\n   *\n   * WARNING: Options specified here will override any properties of same names when request is sent by {@link HttpClient}.\n   */\n  requestOverrides?: Record<string, unknown>;\n}\n\n/**\n * Creates a new pipeline request with the given options.\n * This method is to allow for the easy setting of default values and not required.\n * @param options - The options to create the request with.\n */\nexport function createPipelineRequest(options: PipelineRequestOptions): PipelineRequest {\n  // Cast required due to difference between ts-http-runtime requiring AbortSignal while core-rest-pipeline allows\n  // the more generic AbortSignalLike. The wrapAbortSignalLike pipeline policy will take care of ensuring that any AbortSignalLike in the request\n  // is converted into a true AbortSignal.\n  return tspCreatePipelineRequest(options as TspPipelineRequestOptions);\n}\n"]}