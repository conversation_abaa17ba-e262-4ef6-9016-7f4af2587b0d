"use strict";
// Copyright (c) Microsoft Corporation.
// Licensed under the MIT License.
Object.defineProperty(exports, "__esModule", { value: true });
exports.formDataPolicyName = void 0;
exports.formDataPolicy = formDataPolicy;
const policies_1 = require("@typespec/ts-http-runtime/internal/policies");
/**
 * The programmatic identifier of the formDataPolicy.
 */
exports.formDataPolicyName = policies_1.formDataPolicyName;
/**
 * A policy that encodes FormData on the request into the body.
 */
function formDataPolicy() {
    return (0, policies_1.formDataPolicy)();
}
//# sourceMappingURL=formDataPolicy.js.map