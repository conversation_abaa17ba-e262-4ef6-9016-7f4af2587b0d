#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
表头背景色检测工具
检测所有模板文件中的表头样式，找出使用固定背景色的表头
"""

import os
import re
import json
from pathlib import Path

def find_table_headers_with_background():
    """检测所有模板中的表头背景色"""
    
    templates_dir = Path("app/templates")
    results = {
        "inline_styles": [],      # 内联样式
        "css_classes": [],        # CSS类名
        "style_blocks": [],       # 样式块
        "summary": {
            "total_files": 0,
            "files_with_headers": 0,
            "inline_bg_count": 0,
            "css_class_count": 0,
            "style_block_count": 0
        }
    }
    
    # 匹配模式
    patterns = {
        # 内联样式中的背景色
        "inline_bg": re.compile(r'<th[^>]*style="[^"]*background[^"]*"[^>]*>', re.IGNORECASE),
        "inline_bg_color": re.compile(r'background-color\s*:\s*([^;"\s]+)', re.IGNORECASE),
        "inline_bg_simple": re.compile(r'background\s*:\s*([^;"\s]+)', re.IGNORECASE),
        
        # 表头标签
        "th_tags": re.compile(r'<th[^>]*>', re.IGNORECASE),
        
        # 样式块中的表头样式
        "style_block": re.compile(r'<style[^>]*>(.*?)</style>', re.DOTALL | re.IGNORECASE),
        "th_styles": re.compile(r'\.?[^{]*th[^{]*\{[^}]*background[^}]*\}', re.IGNORECASE),
        
        # CSS类名
        "class_attr": re.compile(r'class="([^"]*)"', re.IGNORECASE),
    }
    
    def extract_background_color(style_text):
        """提取背景色值"""
        bg_match = patterns["inline_bg_color"].search(style_text)
        if bg_match:
            return bg_match.group(1).strip()
        
        bg_simple = patterns["inline_bg_simple"].search(style_text)
        if bg_simple:
            return bg_simple.group(1).strip()
        
        return None
    
    def process_file(file_path):
        """处理单个文件"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except Exception as e:
            print(f"读取文件失败 {file_path}: {e}")
            return False
        
        relative_path = str(file_path.relative_to(templates_dir))
        has_headers = False
        
        # 检查内联样式
        inline_matches = patterns["inline_bg"].findall(content)
        for match in inline_matches:
            has_headers = True
            bg_color = extract_background_color(match)
            results["inline_styles"].append({
                "file": relative_path,
                "tag": match,
                "background_color": bg_color,
                "line_number": content[:content.find(match)].count('\n') + 1
            })
            results["summary"]["inline_bg_count"] += 1
        
        # 检查样式块
        style_blocks = patterns["style_block"].findall(content)
        for style_block in style_blocks:
            th_styles = patterns["th_styles"].findall(style_block)
            for th_style in th_styles:
                has_headers = True
                results["style_blocks"].append({
                    "file": relative_path,
                    "style": th_style.strip(),
                    "line_number": content[:content.find(th_style)].count('\n') + 1
                })
                results["summary"]["style_block_count"] += 1
        
        # 检查所有th标签的类名
        th_matches = patterns["th_tags"].findall(content)
        for th_match in th_matches:
            has_headers = True
            class_match = patterns["class_attr"].search(th_match)
            if class_match:
                classes = class_match.group(1).split()
                for cls in classes:
                    if cls and cls not in ['sortable', 'text-center', 'text-left', 'text-right']:
                        results["css_classes"].append({
                            "file": relative_path,
                            "tag": th_match,
                            "class": cls,
                            "line_number": content[:content.find(th_match)].count('\n') + 1
                        })
                        results["summary"]["css_class_count"] += 1
        
        return has_headers
    
    # 遍历所有HTML文件
    for file_path in templates_dir.rglob("*.html"):
        results["summary"]["total_files"] += 1
        if process_file(file_path):
            results["summary"]["files_with_headers"] += 1
    
    return results

def generate_report(results):
    """生成检测报告"""
    
    print("=" * 80)
    print("表头背景色检测报告")
    print("=" * 80)
    
    # 统计信息
    summary = results["summary"]
    print(f"\n📊 统计信息:")
    print(f"   总文件数: {summary['total_files']}")
    print(f"   包含表头的文件数: {summary['files_with_headers']}")
    print(f"   内联背景色数量: {summary['inline_bg_count']}")
    print(f"   样式块背景色数量: {summary['style_block_count']}")
    print(f"   CSS类名数量: {summary['css_class_count']}")
    
    # 内联样式详情
    if results["inline_styles"]:
        print(f"\n🎨 内联背景色样式 ({len(results['inline_styles'])} 个):")
        for item in results["inline_styles"]:
            print(f"   📁 {item['file']}:{item['line_number']}")
            print(f"      背景色: {item['background_color']}")
            print(f"      标签: {item['tag'][:100]}...")
            print()
    
    # 样式块详情
    if results["style_blocks"]:
        print(f"\n🎨 样式块中的表头样式 ({len(results['style_blocks'])} 个):")
        for item in results["style_blocks"]:
            print(f"   📁 {item['file']}:{item['line_number']}")
            print(f"      样式: {item['style'][:100]}...")
            print()
    
    # CSS类名统计
    if results["css_classes"]:
        print(f"\n🏷️  表头CSS类名 ({len(results['css_classes'])} 个):")
        class_counts = {}
        for item in results["css_classes"]:
            cls = item["class"]
            if cls not in class_counts:
                class_counts[cls] = []
            class_counts[cls].append(item["file"])
        
        for cls, files in sorted(class_counts.items()):
            print(f"   .{cls} (使用 {len(files)} 次)")
            for file in sorted(set(files))[:5]:  # 只显示前5个文件
                print(f"      - {file}")
            if len(set(files)) > 5:
                print(f"      ... 还有 {len(set(files)) - 5} 个文件")
            print()

def save_results(results, filename="table_headers_report.json"):
    """保存检测结果到JSON文件"""
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    print(f"\n💾 详细结果已保存到: {filename}")

def main():
    """主函数"""
    print("🔍 开始检测表头背景色...")
    
    results = find_table_headers_with_background()
    generate_report(results)
    save_results(results)
    
    print("\n✅ 检测完成！")
    
    # 生成修改建议
    print("\n💡 修改建议:")
    if results["inline_styles"]:
        print("   1. 将内联背景色样式改为 var(--theme-primary, #007bff)")
    if results["style_blocks"]:
        print("   2. 将样式块中的固定背景色改为主题色变量")
    if results["css_classes"]:
        print("   3. 检查CSS类的定义，确保使用主题色变量")

if __name__ == "__main__":
    main()
